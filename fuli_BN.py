# --- START OF FILE ARGM_V6_Singularity.py ---

# ------------------------------------------------------------------------------------
# ARGM-V6.0 "Singularity" - 自适应趋势网格复投策略
# ------------------------------------------------------------------------------------
# 作者: Lens & AI
# 日期: 2025-07-11
# 描述: 本策略是基于第一性原理的自适应网格系统。
#       它将网格的位置、范围、密度及重绘决策统一到一个核心函数中，
#       在捕捉均值回归利润的同时，集成了趋势过滤、智能风险控制和利润复投机制。
# 版本历史:
# V6.0: 整合入专业的异步交易框架，替换原有的做市逻辑。
#       - 引入单一决策源函数 generate_grid_state()。
#       - 引入趋势方向手动覆盖开关。
#       - 集成智能风险控制与盈利复投模块。
#       - 优化重绘逻辑为重叠度检测，大幅降低交易成本。
# ------------------------------------------------------------------------------------

import asyncio
import websockets
import json
import logging
import time
import math
import os
import sys
import uuid
import pandas as pd
import numpy as np
from collections import namedtuple
import aiohttp
import ccxt.async_support as ccxt_async


# ====================================================================================
# --- 策略配置 (所有可调参数集中于此) ---
# ====================================================================================
CONFIG = {
    # --- 账户与连接配置 ---
    "api_key": "USlBuHx9zNwxXFtecEQZDOXXxbHhKMIViMeRaELIyjOHGCcdkoESgmxpu4bWi47c",      # ❗❗❗ 替换为你的 API Key
    "api_secret": "lxNiaI54wlWAyxpZ5oNajJ84MoQYodnakDWfoQiuONNTtU9YKsKS14tRwgBi7eUt",  # ❗❗❗ 替换为你的 API Secret
    "use_proxy": True,
    "proxy_url": "http://127.0.0.1:7897",

    # --- 策略核心参数 ---
    "coin_name": "SOL",
    "contract_type": "USDT",
    "leverage": 20,
    "initial_value": 20, # [重构] 含义变为：每格订单的基础名义价值

    # --- [新增] ARGM-V6.0 核心逻辑配置 ---
    "argm": {
        "ema_trend_period": 200,      # 宏观趋势判定周期
        "ema_anchor_period": 96,       # 网格中枢平滑周期
        "atr_period": 14,              # 波动率计算周期
        "rsi_period": 14,              # 动能/情绪计算周期
        "grid_total_levels": 30,       # 网格总层数 (固定值，密度由区间宽度自适应)
        "repaint_overlap_ratio": 0.6,  # 新旧网格重合度低于此值则重绘
        "kline_timeframe": '1h',       # 用于计算指标的K线周期
        # [新增] 趋势方向手动覆盖开关: "AUTO", "LONG_ONLY", "SHORT_ONLY"
        "trend_override": "LONG_ONLY",
        "strategy_execution_interval_seconds": 10.0,  # 策略执行最小间隔(秒)，防止过度频繁执行
    },

    # --- [新增] 智能风险与复投配置 ---
    "risk_management": {
        # ✅ 多/空头寸合约数量的硬性上限，这是最重要的风控参数
        "position_limit_contracts": 0.5,
        # 智能风险恢复模块参数
        "smart_recovery_config": {'add_multiplier': 1.3, 'reduce_multiplier': 0.7, 'pause_threshold': 3, 'pause_bars': 4},
        # 盈利复投模块参数
        "reinvest_config": {'threshold_usd': 500, 'profit_ratio_multiplier': 0.2, 'size_cap_multiplier': 2.0}
    },

    # --- 系统运行参数 (基本保持不变) ---
    "system": {
        "sync_interval_seconds": 60,
        "timeout_seconds": 30,
        "ticker_update_interval_ms": 500, # 适当放宽，避免过于频繁调整
        "max_connection_retries": 5,
        "connection_retry_delay": 5,
        "state_sync_on_error": True,
        "websocket_ping_interval": 20,
        "websocket_timeout": 60
    }
}


# ====================================================================================
# --- 日志配置 (保持不变) ---
# ====================================================================================
if not os.path.exists("log"):
    os.makedirs("log")
script_name = os.path.splitext(os.path.basename(__file__))[0]
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - [%(funcName)s] - %(message)s",
    handlers=[
        logging.FileHandler(f"log/{script_name}.log", encoding="utf-8"),
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger()


# ====================================================================================
# --- 主策略类 ---
# ====================================================================================
class ARGMStrategyBot:
    # [新增] 定义GridState数据结构
    GridState = namedtuple('GridState', ['grids', 'center', 'low', 'high', 'should_repaint'])

    def __init__(self, config):
        """初始化策略实例"""
        self.config = config
        self.lock = asyncio.Lock()
        self.order_semaphore = asyncio.Semaphore(3)  # 限制并发下单数量

        self.exchange = self._initialize_exchange()
        # 修复：使用正确的期货交易对格式
        self.symbol = f"{config['coin_name']}/USDT:USDT"  # 期货交易对格式
        self.market = None
        
        contract_type = self.config['contract_type'].upper()
        if contract_type in ['USDT', 'USDC']:
            self.websocket_base_url = "wss://fstream.binance.com"
        else:
            logger.error(f"不支持的合约类型: {contract_type}"); sys.exit(1)
        logger.info(f"已设置WebSocket URL: {self.websocket_base_url}")

        # --- 实时数据状态 ---
        self.best_bid_price = 0.0
        self.best_ask_price = 0.0
        self.last_price = 0.0
        self.price_series = pd.DataFrame(columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])

        # --- 核心策略状态 ---
        self.long_position_size = 0.0
        self.short_position_size = 0.0
        self.net_inventory = 0.0
        self.open_orders = {}
        self.current_grids = [] # [新增] 当前生效的网格

        # [新增] 智能风控与复投状态
        self.consecutive_losses = 0
        self.total_profit_usd = 0.0
        self.is_paused = False
        self.pause_end_time = 0
        self._last_grid_price = 0.0  # 用于网格重绘频率控制

        # --- 系统控制变量 ---
        self.listen_key = None
        self.last_state_sync_time = 0
        self.last_ticker_time = 0
        self.last_strategy_execution_time = 0  # 新增：策略执行频率控制
        self.is_shutting_down = False

    def _initialize_exchange(self):
        """初始化并返回CCXT交易所实例"""
        if "YOUR_API_KEY" in self.config['api_key'] or "YOUR_API_SECRET" in self.config['api_secret']:
            logger.error("API密钥未配置，请在CONFIG中设置后再运行。")
            sys.exit(1)

        exchange_class = getattr(ccxt_async, self.config.get('exchange_id', 'binanceusdm'))

        exchange_config = {
            'apiKey': self.config['api_key'],
            'secret': self.config['api_secret'],
            'options': {
                'defaultType': 'future',
                'adjustForTimeDifference': True,
                'recvWindow': 10000,
                'warnOnFetchOpenOrdersWithoutSymbol': False,
                'createMarketBuyOrderRequiresPrice': False
            },
            'timeout': 120000,
            'enableRateLimit': True,
            'rateLimit': 1200,
            'headers': {
                'User-Agent': 'Mozilla/5.0'
            }
        }

        if self.config.get('use_proxy') and self.config.get('proxy_url'):
            exchange_config['aiohttp_proxy'] = self.config['proxy_url']
            logger.info(f"已通过 aiohttp_proxy 设置代理: {self.config['proxy_url']}")

        exchange = exchange_class(exchange_config)
        logger.info("交易所实例创建成功")
        return exchange
    
    async def test_connection(self):
        """测试网络连接和API可用性"""
        max_retries = 5  # 增加重试次数
        retry_delay = 3
        
        for attempt in range(max_retries):
            try:
                logger.info(f"正在测试API连接... (尝试 {attempt + 1}/{max_retries})")
                
                # 1. 首先测试基础网络连接
                logger.info("正在测试基础网络连接...")
                proxy = self.config['proxy_url'] if self.config.get('use_proxy') else None
                
                # 使用更长的超时时间和更好的错误处理
                connector = aiohttp.TCPConnector(
                    limit=100,
                    limit_per_host=30,
                    ttl_dns_cache=300,
                    use_dns_cache=True,
                    keepalive_timeout=30,
                    enable_cleanup_closed=True
                )
                
                timeout = aiohttp.ClientTimeout(
                    total=60,  # 总超时时间
                    connect=30,  # 连接超时
                    sock_read=30  # 读取超时
                )
                
                async with aiohttp.ClientSession(
                    connector=connector,
                    timeout=timeout,
                    headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
                ) as session:
                    # 测试多个端点以确保连接稳定
                    test_urls = [
                        "https://fapi.binance.com/fapi/v1/ping",
                        "https://fapi.binance.com/fapi/v1/time"
                    ]
                    
                    for test_url in test_urls:
                        try:
                            logger.info(f"测试端点: {test_url}")
                            async with session.get(test_url, proxy=proxy) as response:
                                if response.status == 200:
                                    result = await response.json()
                                    logger.info(f"端点测试成功: {test_url} - {result}")
                                    break
                                else:
                                    logger.warning(f"端点返回状态码: {response.status}")
                        except Exception as url_error:
                            logger.error(f"端点测试失败 {test_url}: {url_error}")
                            if test_url == test_urls[-1]:  # 最后一个URL也失败
                                raise url_error
                            continue
                
                # 2. 测试CCXT交易所连接
                logger.info("正在测试CCXT交易所连接...")
                try:
                    # 使用更简单的ping方法
                    ping_result = await asyncio.wait_for(
                        self.exchange.publicGetPing(), timeout=45
                    )
                    logger.info(f"CCXT Ping 成功: {ping_result}")
                except Exception as ping_error:
                    logger.warning(f"CCXT Ping失败，尝试备用方法: {ping_error}")
                    # 尝试直接调用时间API
                    try:
                        time_result = await asyncio.wait_for(
                            self.exchange.publicGetTime(), timeout=45
                        )
                        logger.info(f"时间API调用成功: {time_result}")
                    except Exception as time_error:
                        logger.error(f"时间API也失败: {time_error}")
                        raise time_error
                
                # 3. 测试服务器时间获取
                logger.info("正在测试服务器时间获取...")
                try:
                    server_time = await asyncio.wait_for(
                        self.exchange.fetch_time(), timeout=45
                    )
                    logger.info(f"服务器时间获取成功: {server_time}")
                except Exception as time_error:
                    logger.warning(f"服务器时间获取失败: {time_error}")
                    # 继续尝试其他测试

                # 4. 加载市场数据
                logger.info("正在加载市场数据...")
                try:
                    await asyncio.wait_for(self.exchange.load_markets(), timeout=60)
                    logger.info("市场数据加载成功")

                    # 验证交易对是否存在
                    if self.exchange.markets and self.symbol in self.exchange.markets:
                        logger.info(f"交易对 {self.symbol} 验证成功")
                    else:
                        logger.warning(f"交易对 {self.symbol} 不存在，尝试查找替代交易对...")
                        if self.exchange.markets:
                            # 尝试多种交易对格式
                            possible_symbols = [
                                f"{self.config['coin_name']}/USDT:USDT",  # 期货格式
                                f"{self.config['coin_name']}/USDT",       # 现货格式
                                f"{self.config['coin_name']}USDT",        # 简化格式
                            ]
                            
                            found_symbol = None
                            for test_symbol in possible_symbols:
                                if test_symbol in self.exchange.markets:
                                    found_symbol = test_symbol
                                    break
                            
                            if found_symbol:
                                logger.info(f"找到可用交易对: {found_symbol}")
                                self.symbol = found_symbol
                            else:
                                # 显示可用的USDT交易对
                                available_symbols = [s for s in list(self.exchange.markets.keys())[:10] if 'USDT' in s]
                                logger.warning("可用的USDT交易对示例:")
                                for sym in available_symbols:
                                    logger.warning(f"  - {sym}")
                                logger.error(f"无法找到 {self.config['coin_name']} 相关的交易对")
                        else:
                            logger.error("市场数据为空")

                except Exception as market_error:
                    logger.warning(f"市场数据加载失败: {market_error}")
                    logger.warning("市场数据加载失败，但继续测试...")

                # 5. 测试ticker数据获取
                logger.info("正在测试ticker数据获取...")
                try:
                    ticker = await asyncio.wait_for(
                        self.exchange.fetch_ticker(self.symbol), timeout=45
                    )
                    logger.info(f"Ticker 获取成功: {ticker['symbol']} @ {ticker['last']}")
                except Exception as ticker_error:
                    logger.warning(f"Ticker获取失败: {ticker_error}")
                    logger.warning("Ticker获取失败，但这可能是交易对问题，继续测试...")
                
                # 6. 测试账户余额获取 (需要API密钥)
                logger.info("正在测试账户余额获取...")
                try:
                    balance = await asyncio.wait_for(
                        self.exchange.fetch_balance(), timeout=45
                    )
                    usdt_balance = balance.get('USDT', {}).get('total', 'N/A')
                    logger.info(f"余额获取成功，USDT余额: {usdt_balance}")
                    
                    # 检查余额是否足够
                    if isinstance(usdt_balance, (int, float)) and usdt_balance < 10:
                        logger.warning(f"USDT余额较低: {usdt_balance}，请确保有足够资金进行交易")
                        
                except Exception as balance_error:
                    logger.warning(f"余额获取失败: {balance_error}")
                    logger.warning("余额获取失败，但这可能是API权限问题，继续运行...")
                
                logger.info("API连接测试完成！")
                return True
                
            except asyncio.TimeoutError:
                logger.error(f"连接测试超时 (尝试 {attempt + 1}/{max_retries})")
            except ccxt_async.AuthenticationError as e:
                logger.error(f"API密钥认证失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                logger.error("请检查API密钥和密钥权限设置")
            except (ccxt_async.NetworkError, ccxt_async.RequestTimeout) as e:
                logger.error(f"网络连接失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if self.config.get('use_proxy'):
                    logger.error("请检查代理服务器是否正常运行")
            except Exception as e:
                logger.error(f"连接测试失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                logger.error(f"详细错误信息: {type(e).__name__}: {str(e)}")
            
            if attempt < max_retries - 1:
                logger.info(f"等待 {retry_delay} 秒后重试...")
                await asyncio.sleep(retry_delay)
        
        logger.error("所有连接测试尝试均失败")
        return False

    # --------------------------------------------------------------------------
    # --- 启动与生命周期管理 ---
    # --------------------------------------------------------------------------
    async def setup(self):
        """执行所有异步的启动设置"""
        logger.info("正在执行启动设置...")
        
        try:
            if not await self.test_connection(): raise Exception("API连接测试失败")
            
            logger.info("正在加载市场数据...")
            await self.exchange.load_markets(True)
            if not self.exchange.markets or self.symbol not in self.exchange.markets:
                raise Exception(f"交易对 {self.symbol} 不存在")
            self.market = self.exchange.market(self.symbol)
            logger.info(f"成功加载市场数据，合约精度: {self.market['precision']}")
            
            await self.set_hedge_mode()
            await self.set_leverage()
            await self.full_state_sync()
            
            if self.last_price == 0: self.last_price = (await self.exchange.fetch_ticker(self.symbol))['last']
            
            await self.update_price_series()
            
            self.listen_key = await self.fetch_listen_key()
            asyncio.create_task(self.keep_listen_key_alive())
            
            logger.info("初始化设置完成。")
            logger.info(f"当前持仓: 多头={self.long_position_size}, 空头={self.short_position_size}")
            
        except Exception as e:
            logger.error(f"初始化设置失败: {e}", exc_info=True)
            raise

    async def run(self):
        """策略主循环，负责WebSocket连接和消息处理"""
        await self.setup()
        await self.cancel_all_open_orders()
        logger.info("所有现有挂单已取消，准备启动策略...")
        
        connection_failures = 0
        max_connection_failures = 10
        
        while not self.is_shutting_down:
            try:
                stream_url = self.websocket_base_url + f"/ws/{self.listen_key}"
                logger.info(f"正在连接WebSocket: {stream_url}")
                
                # 增加WebSocket连接的超时和重试配置
                additional_headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
                
                async with websockets.connect(
                    stream_url, 
                    ping_interval=30,  # 增加ping间隔
                    ping_timeout=10,   # ping超时
                    close_timeout=10,  # 关闭超时
                    max_size=2**20,    # 最大消息大小
                    additional_headers=additional_headers
                ) as websocket:
                    logger.info("WebSocket 连接成功。")
                    connection_failures = 0  # 重置失败计数
                    
                    # 订阅ticker数据流
                    market_id = self.market['id'].lower() if self.market and 'id' in self.market else (self.config['coin_name'].lower() + 'usdt')
                    ticker_payload = {"method": "SUBSCRIBE", "params": [f"{market_id}@bookTicker"], "id": 1}
                    
                    try:
                        await asyncio.wait_for(websocket.send(json.dumps(ticker_payload)), timeout=10)
                        logger.info(f"已发送Ticker订阅请求: {ticker_payload}")
                    except Exception as send_error:
                        logger.error(f"发送订阅请求失败: {send_error}")
                        continue
                    
                    await self.full_state_sync() # 连接成功后立即同步一次
                    
                    # 消息接收循环
                    while not self.is_shutting_down:
                        try:
                            message = await asyncio.wait_for(websocket.recv(), timeout=90)  # 增加超时时间
                            data = json.loads(message)
                            
                            # 处理不同类型的消息
                            if 'e' in data:
                                await self.handle_websocket_message(data)
                            elif 'result' in data:
                                logger.debug(f"订阅确认: {data}")
                            elif 'error' in data:
                                logger.error(f"WebSocket错误: {data}")
                                break
                                
                        except asyncio.TimeoutError:
                            logger.warning("WebSocket接收消息超时，检查连接状态...")
                            # 发送ping检查连接
                            try:
                                await asyncio.wait_for(websocket.ping(), timeout=5)
                                logger.debug("WebSocket ping成功，连接正常")
                                continue
                            except Exception:
                                logger.warning("WebSocket ping失败，连接可能已断开，将重连")
                                break
                                
                        except websockets.exceptions.ConnectionClosed as e:
                            logger.warning(f"WebSocket连接已关闭 ({e.code}): {e.reason}")
                            break
                        except json.JSONDecodeError as e:
                            logger.warning(f"JSON解析失败: {e}")
                            continue
                        except Exception as e:
                            logger.error(f"处理WebSocket消息时出错: {e}")
                            continue
            
            except Exception as e:
                connection_failures += 1
                logger.error(f"WebSocket连接失败 ({connection_failures}/{max_connection_failures}): {e}")
                
                if connection_failures >= max_connection_failures:
                    logger.critical("WebSocket连接失败次数过多，程序退出")
                    break
            
            if self.is_shutting_down: break
            
            # 计算重连延迟（指数退避）
            retry_delay = min(self.config['system']['connection_retry_delay'] * (2 ** min(connection_failures, 5)), 60)
            logger.info(f"等待 {retry_delay} 秒后重连...")
            await asyncio.sleep(retry_delay)
            
            # 重新获取listen key
            try:
                self.listen_key = await self.fetch_listen_key()
                logger.info("已获取新的ListenKey")
            except Exception as lk_e:
                logger.error(f"重连时获取ListenKey失败: {lk_e}")
                connection_failures += 1


    async def close(self):
        """优雅关闭程序，取消所有挂单并关闭连接"""
        if self.is_shutting_down: return
        self.is_shutting_down = True
        logger.info("正在关闭程序...")
        
        # 尝试取消挂单，但不要因为失败而阻止程序关闭
        try:
            if self.exchange and hasattr(self, 'market') and self.market:
                await asyncio.wait_for(self.cancel_all_open_orders(), timeout=10)
        except Exception as e:
            logger.warning(f"关闭时取消挂单失败，但程序将继续关闭: {e}")
        
        # 关闭交易所连接
        try:
            if self.exchange:
                await self.exchange.close()
        except Exception as e:
            logger.warning(f"关闭交易所连接时出错: {e}")
            
        logger.info("程序已关闭。")

    # --------------------------------------------------------------------------
    # --- 核心交易逻辑 ---
    # --------------------------------------------------------------------------
    async def handle_websocket_message(self, data):
        """处理来自WebSocket的消息，分发到不同的处理器"""
        try:
            # 检查数据有效性
            if not isinstance(data, dict):
                logger.debug(f"收到非字典类型消息: {type(data)}")
                return
                
            event_type = data.get("e")
            
            # 处理ticker数据
            if event_type == "bookTicker":
                # 验证必要字段
                if all(key in data for key in ['b', 'a']):
                    current_time_ms = time.time() * 1000
                    if current_time_ms - self.last_ticker_time > self.config['system']['ticker_update_interval_ms']:
                        self.last_ticker_time = current_time_ms
                        await self.handle_ticker_update(data)
                else:
                    logger.warning(f"Ticker数据缺少必要字段: {data}")
                    
            # 处理订单更新
            elif event_type == "ORDER_TRADE_UPDATE":
                if 'o' in data:
                    await self.handle_order_update(data)
                else:
                    logger.warning(f"订单更新消息缺少'o'字段: {data}")
                    
            # 处理ListenKey过期
            elif event_type == "listenKeyExpired":
                logger.warning("ListenKey 已过期，主循环将自动重连。")
                raise websockets.exceptions.ConnectionClosed(None, None)
                
            # 处理账户更新
            elif event_type == "ACCOUNT_UPDATE":
                logger.info(f"收到账户更新: {data}")
                
            # 处理其他消息类型
            elif event_type:
                logger.debug(f"收到未处理的事件类型: {event_type}")
            else:
                # 可能是订阅确认或其他系统消息
                if 'result' in data:
                    logger.debug(f"收到订阅确认: {data}")
                elif 'error' in data:
                    logger.error(f"WebSocket错误消息: {data}")
                else:
                    logger.debug(f"收到未知消息格式: {data}")
                    
        except Exception as e:
            logger.error(f"处理WebSocket消息时发生错误: {e}")
            logger.error(f"错误类型: {type(e).__name__}")
            logger.debug(f"问题消息: {data}")

    async def handle_ticker_update(self, data):
        """处理行情更新并触发策略调整"""
        try:
            # 验证数据完整性
            if not all(key in data for key in ['b', 'a']):
                logger.warning(f"Ticker数据缺少必要字段: {data}")
                return
                
            # 解析价格数据
            try:
                bid_price = float(data['b'])
                ask_price = float(data['a'])
                
                # 验证价格合理性
                if bid_price <= 0 or ask_price <= 0 or bid_price > ask_price:
                    logger.warning(f"价格数据异常: bid={bid_price}, ask={ask_price}")
                    return
                    
                self.best_bid_price = bid_price
                self.best_ask_price = ask_price
                self.last_price = (self.best_bid_price + self.best_ask_price) / 2
                
            except (ValueError, TypeError) as e:
                logger.error(f"价格数据转换失败: {e}, 数据: {data}")
                return
            
            # 定期同步状态
            current_time = time.time()
            if current_time - self.last_state_sync_time > self.config['system']['sync_interval_seconds']:
                try:
                    await self.full_state_sync()
                    await self.update_price_series()
                except Exception as e:
                    logger.error(f"状态同步失败: {e}")
                    # 继续执行策略，不因同步失败而中断

            # 执行策略
            try:
                await self.execute_argm_strategy()
            except Exception as e:
                logger.error(f"策略执行失败: {e}")
                
        except Exception as e:
            logger.error(f"处理ticker更新时发生未预期错误: {e}")
            logger.error(f"错误类型: {type(e).__name__}")
            logger.debug(f"Ticker数据: {data}")

    async def execute_argm_strategy(self):
        """ARGM V6.0 核心策略执行入口"""
        try:
            # 策略执行频率控制 - 防止过度频繁执行
            current_time = time.time()
            strategy_interval = self.config.get('argm', {}).get('strategy_execution_interval_seconds', 2.0)  # 默认2秒间隔
            if current_time - self.last_strategy_execution_time < strategy_interval:
                logger.debug(f"策略执行间隔未到，跳过本次执行 (间隔: {strategy_interval}s)")
                return

            self.last_strategy_execution_time = current_time

            # 检查基础条件（无需锁）
            if not self.last_price or self.last_price <= 0:
                logger.debug("价格数据无效，跳过策略执行")
                return

            if self.price_series is None or len(self.price_series) < 20:
                logger.debug("价格序列数据不足，跳过策略执行")
                return

            # 检查暂停状态（需要锁保护）
            should_skip = False
            async with self.lock:
                current_time = time.time()
                if self.is_paused and current_time < self.pause_end_time:
                    logger.debug(f"策略暂停中，剩余 {self.pause_end_time - current_time:.0f} 秒")
                    should_skip = True
                elif self.is_paused:
                    logger.info("暂停期结束，恢复策略运行。")
                    self.is_paused = False
                    self.consecutive_losses = 0

            if should_skip:
                return

            # 生成网格状态（无需锁，只读操作）
            try:
                grid_state = self.generate_grid_state(self.price_series, self.current_grids)
                if not grid_state or not grid_state.grids:
                    logger.warning("网格状态生成失败，跳过本次执行")
                    return
            except Exception as e:
                logger.error(f"生成网格状态失败: {e}")
                return

            # 执行网格调整（在锁外执行，避免长时间占用锁）
            try:
                if grid_state.should_repaint:
                    logger.info(f"网格重绘触发！新中枢: {grid_state.center:.4f}, 新范围: [{grid_state.low:.4f}, {grid_state.high:.4f}]")
                    # 在锁内快速更新网格状态
                    async with self.lock:
                        self.current_grids = grid_state.grids
                    # 在锁外执行订单调整
                    await self.rebalance_grid_orders(grid_state.grids)
                else:
                    # 如果不需要重绘，检查是否有必要调整订单
                    # 只有在网格重绘时才执行订单调整，避免频繁无意义的调整
                    logger.debug("网格无需重绘，跳过订单调整")
            except Exception as e:
                logger.error(f"网格订单调整失败: {e}")

        except Exception as e:
            logger.error(f"策略执行过程中发生未预期错误: {e}")
            logger.error(f"错误类型: {type(e).__name__}")

    def generate_grid_state(self, price_series, old_grids):
        """单一决策源：计算并决定网格的最终形态"""
        cfg = self.config['argm']
        
        try:
            # 检查价格数据是否足够
            if len(price_series) < max(cfg['ema_anchor_period'], cfg['atr_period'], cfg['rsi_period']):
                logger.debug("价格数据不足，使用旧网格")
                return self.GridState(grids=old_grids, center=0, low=0, high=0, should_repaint=False)
                
            # 如果有旧网格且价格变化不大，可能不需要重新计算
            if old_grids and len(old_grids) > 0:
                current_price = price_series['close'].iloc[-1]
                # 简单检查：如果价格变化小于1%，先尝试保持现有网格
                if hasattr(self, '_last_grid_price'):
                    price_change_pct = abs(current_price - self._last_grid_price) / self._last_grid_price
                    if price_change_pct < 0.01:  # 价格变化小于1%
                        logger.debug(f"价格变化较小({price_change_pct:.3%})，保持现有网格")
                        return self.GridState(grids=old_grids, center=0, low=0, high=0, should_repaint=False)
                        
            # 记录当前价格用于下次比较
            self._last_grid_price = price_series['close'].iloc[-1]
            anchor = price_series['close'].ewm(span=cfg['ema_anchor_period'], adjust=False).mean().iloc[-1]
            
            high_low = price_series['high'] - price_series['low']
            high_close = abs(price_series['high'] - price_series['close'].shift())
            low_close = abs(price_series['low'] - price_series['close'].shift())
            tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
            atr = tr.ewm(alpha=1/cfg['atr_period'], adjust=False).mean().iloc[-1]

            delta = price_series['close'].diff()
            gain = (delta.where(delta > 0, 0)).ewm(alpha=1/cfg['rsi_period'], adjust=False).mean()
            loss = (-delta.where(delta < 0, 0)).ewm(alpha=1/cfg['rsi_period'], adjust=False).mean()

            # 防止除零错误
            loss_value = loss.iloc[-1]
            if loss_value == 0 or pd.isna(loss_value):
                rsi = 100  # 如果没有损失，RSI设为100
            else:
                rs = gain.iloc[-1] / loss_value
                rsi = 100 - (100 / (1 + rs))

            # 确保RSI在合理范围内
            rsi = max(0, min(100, rsi))
            rsi_normalized = (rsi - 50) / 50
            volatility_factor = 2.5 + abs(rsi_normalized) * 1.5

            grid_low = anchor - (atr * volatility_factor)
            grid_high = anchor + (atr * volatility_factor)

            new_grids = self.generate_nonlinear_grids(center=anchor, low=grid_low, high=grid_high, num_levels=cfg['grid_total_levels'])
            
            overlap_ratio = self.calculate_grid_overlap(new_grids, old_grids)
            should_repaint_flag = overlap_ratio < cfg['repaint_overlap_ratio'] if old_grids else True

            return self.GridState(
                grids=new_grids, center=anchor, low=grid_low, high=grid_high, should_repaint=should_repaint_flag
            )
        except Exception as e:
            logger.error(f"生成网格状态失败: {e}", exc_info=True)
            return self.GridState(grids=old_grids, center=0, low=0, high=0, should_repaint=False)


    def get_trading_direction(self):
        """根据配置决定交易方向"""
        override = self.config['argm']['trend_override'].upper()
        if override in ["LONG_ONLY", "SHORT_ONLY"]: return override
        
        ema_trend = self.price_series['close'].ewm(span=self.config['argm']['ema_trend_period'], adjust=False).mean().iloc[-1]
        return "LONG_ONLY" if self.last_price > ema_trend else "SHORT_ONLY"

    def get_next_order_size(self):
        """统一决定下一次交易的仓位大小"""
        base_size_usd = self.config['initial_value']
        risk_cfg = self.config['risk_management']['smart_recovery_config']
        reinv_cfg = self.config['risk_management']['reinvest_config']

        if self.consecutive_losses >= risk_cfg['pause_threshold']:
            if not self.is_paused:
                self.is_paused = True
                kline_interval_minutes = int(self.config['argm']['kline_timeframe'][:-1])
                self.pause_end_time = time.time() + risk_cfg['pause_bars'] * kline_interval_minutes * 60
                logger.warning(f"连续亏损达到 {self.consecutive_losses} 次，暂停交易 {risk_cfg['pause_bars'] * kline_interval_minutes} 分钟。")
            return base_size_usd
        elif self.consecutive_losses == 2: return base_size_usd * risk_cfg['reduce_multiplier']
        elif self.consecutive_losses == 1: return base_size_usd * risk_cfg['add_multiplier']
        
        if self.total_profit_usd > reinv_cfg['threshold_usd']:
            profit_ratio = self.total_profit_usd / (self.config.get('total_capital', 10000)) # 可配置总本金
            reinvested_size = base_size_usd * (1 + profit_ratio * reinv_cfg['profit_ratio_multiplier'])
            return min(reinvested_size, base_size_usd * reinv_cfg['size_cap_multiplier'])
        
        return base_size_usd

    # --------------------------------------------------------------------------
    # --- 订单与状态管理 ---
    # --------------------------------------------------------------------------
    async def rebalance_grid_orders(self, target_grids):
        """对比目标网格与现有订单，执行新增、删除操作（已优化防死锁）"""
        logger.info("=== rebalance_grid_orders函数开始执行 ===")

        # --- 锁外执行I/O密集型操作 ---
        logger.info("开始状态同步 (锁外)...")
        try:
            await asyncio.wait_for(self._internal_state_sync(), timeout=20)
            logger.info("状态同步完成 (锁外)")
        except asyncio.TimeoutError:
            logger.warning("状态同步超时，本次网格调整取消。")
            return
        except Exception as e:
            logger.error(f"状态同步失败，本次网格调整取消: {e}", exc_info=True)
            return

        # --- 锁内执行CPU密集型和内存操作 ---
        logger.debug("准备获取锁进行内存操作...")

        # 先在锁外进行基础验证，减少锁内时间
        if self.best_bid_price <= 0 or self.best_ask_price <= 0:
            logger.warning(f"价格数据无效 (bid={self.best_bid_price}, ask={self.best_ask_price})，跳过订单调整。")
            return

        if not target_grids or not self.last_price or float(self.last_price) <= 0:
            logger.debug("网格参数无效，跳过订单调整")
            return

        # 在锁内快速获取必要信息，然后释放锁
        orders_to_place = []
        orders_to_cancel = []
        order_quantity = 0

        async with self.lock:
            logger.debug("已获取锁，开始执行内存操作...")
            try:
                logger.debug(f"当前挂单: {list(self.open_orders.keys())}")
                logger.debug(f"目标网格价格: {target_grids}")

                # 检查是否有实际变化需要调整
                existing_prices = {float(o.get('price', 0)) for o in self.open_orders.values() if o.get('price') is not None}
                target_prices = {float(price) for price in target_grids if price is not None and float(price) > 0}

                if existing_prices == target_prices:
                    logger.debug("目标网格与现有订单一致，跳过调整")
                    return

                logger.info(f"网格需要调整: 现有{len(existing_prices)}个订单 -> 目标{len(target_prices)}个订单")

                # 获取交易方向和订单大小
                try:
                    direction = self.get_trading_direction()
                    order_size_usd = self.get_next_order_size()

                    if order_size_usd <= 0:
                        logger.warning("订单大小无效，跳过订单调整")
                        return

                    order_quantity = order_size_usd / self.last_price

                    # 验证订单数量合理性
                    if order_quantity <= 0:
                        logger.warning(f"计算的订单数量无效: {order_quantity}")
                        return

                except Exception as e:
                    logger.error(f"获取交易参数失败: {e}", exc_info=True)
                    return

                # 1. 识别需要下的目标订单
                try:
                    existing_prices = {o.get('price') for o in self.open_orders.values() if o.get('price') is not None}
                    logger.info(f"调试信息 - 交易方向: {direction}, 当前价格: {self.last_price}, 最佳买价: {self.best_bid_price}, 最佳卖价: {self.best_ask_price}")
                    logger.info(f"调试信息 - 现有订单价格: {existing_prices}")
                    logger.info(f"调试信息 - 目标网格价格: {target_grids}")

                    for price in target_grids:
                        if price is None or float(price) <= 0:
                            logger.warning(f"无效的网格价格: {price}")
                            continue

                        if price not in existing_prices:
                            logger.debug(f"检查价格 {price}: 不在现有订单中")
                            # 修复网格交易逻辑：对于LONG_ONLY，在当前价格以下放置买单
                            # 对于SHORT_ONLY，在当前价格以上放置卖单
                            if direction == "LONG_ONLY" and float(price) < self.last_price:
                                logger.debug(f"添加买单: {price} < {self.last_price} (当前价格)")
                                orders_to_place.append({'side': 'buy', 'price': price, 'positionSide': 'LONG'})
                            elif direction == "SHORT_ONLY" and float(price) > self.last_price:
                                logger.debug(f"添加卖单: {price} > {self.last_price} (当前价格)")
                                orders_to_place.append({'side': 'sell', 'price': price, 'positionSide': 'SHORT'})
                            else:
                                logger.debug(f"价格 {price} 不满足下单条件: direction={direction}, price vs current={float(price)} vs {self.last_price}")
                        else:
                            logger.debug(f"价格 {price} 已存在于现有订单中")

                    logger.info(f"调试信息 - 识别到需要下单的订单数量: {len(orders_to_place)}")
                    if orders_to_place:
                        logger.info(f"调试信息 - 待下单详情: {orders_to_place}")

                except Exception as e:
                    logger.error(f"识别目标订单失败: {e}")
                    logger.info("=== rebalance_grid_orders函数提前返回(识别目标订单失败) ===")
                    return

                # 2. 识别需要取消的多余订单
                try:
                    target_grids_set = set(target_grids)
                    orders_to_cancel = [o for o in self.open_orders.values()
                                      if o.get('price') is not None and o.get('price') not in target_grids_set]
                except Exception as e:
                    logger.error(f"识别取消订单失败: {e}")
                    logger.info("=== rebalance_grid_orders函数提前返回(识别取消订单失败) ===")
                    return

            except Exception as e:
                logger.error(f"网格订单分析过程中发生未预期错误: {e}")
                logger.error(f"错误类型: {type(e).__name__}")
                logger.debug(f"目标网格: {target_grids}")
                logger.info("=== rebalance_grid_orders函数异常退出 ===")
                return

        # --- 在锁外执行I/O操作，避免死锁 ---
        logger.info(f"开始执行订单操作: 取消{len(orders_to_cancel)}个, 创建{len(orders_to_place)}个")

        # 先取消不需要的订单（串行执行避免并发冲突）
        if orders_to_cancel:
            logger.info(f"准备取消 {len(orders_to_cancel)} 个订单")
            successful_cancels = 0
            for order in orders_to_cancel:
                client_order_id = order.get('clientOrderId')
                if client_order_id:
                    try:
                        logger.debug(f"正在取消订单: {client_order_id}")
                        await self.cancel_order(client_order_id)
                        successful_cancels += 1
                        logger.debug(f"订单取消成功: {client_order_id}")
                        # 短暂延迟避免API限速
                        await asyncio.sleep(0.1)
                    except Exception as e:
                        logger.warning(f"取消订单失败 {client_order_id}: {e}")

            logger.info(f"成功取消 {successful_cancels}/{len(orders_to_cancel)} 个订单")

            # 取消订单后短暂等待状态更新
            if successful_cancels > 0:
                logger.debug("等待订单取消状态更新...")
                await asyncio.sleep(0.5)
        else:
            logger.debug("无需取消订单")

        # 再创建新订单（串行执行避免余额冲突）
        if orders_to_place:
            logger.info(f"准备创建 {len(orders_to_place)} 个新订单")
            successful_places = 0
            for i, order_info in enumerate(orders_to_place):
                try:
                    logger.debug(f"正在创建订单 {i+1}/{len(orders_to_place)}: {order_info['side']} {order_info['positionSide']} @ {order_info['price']}")
                    result = await self.place_order(
                        order_info['side'],
                        order_info['positionSide'],
                        order_quantity,
                        order_info['price']
                    )
                    if result:
                        successful_places += 1
                        logger.debug(f"订单创建成功 {i+1}/{len(orders_to_place)}")
                    else:
                        logger.warning(f"订单创建失败 {i+1}/{len(orders_to_place)}: 返回None")
                    # 短暂延迟避免API限速和余额冲突
                    await asyncio.sleep(0.1)
                except Exception as e:
                    logger.warning(f"创建订单失败 (价格: {order_info['price']}): {e}")
                    # 如果是余额不足，停止后续下单
                    if "insufficient" in str(e).lower() or "balance" in str(e).lower():
                        logger.warning("余额不足，停止后续下单")
                        break

            logger.info(f"成功创建 {successful_places}/{len(orders_to_place)} 个订单")
        else:
            logger.debug("无需创建新订单")

        # 最终状态同步（等待完成确保一致性）
        try:
            await asyncio.wait_for(self._internal_state_sync(), timeout=10)
            logger.debug("网格调整后状态同步完成")
        except asyncio.TimeoutError:
            logger.warning("最终状态同步超时")
        except Exception as e:
            logger.error(f"最终状态同步失败: {e}")

        logger.info("=== rebalance_grid_orders函数正常完成 ===")

    async def handle_order_update(self, data):
        """处理订单更新消息，并更新风控状态"""
        try:
            order_data = data.get("o", {})
            if not self.market or order_data.get("s") != self.market['id']: 
                return
            
            # 验证订单数据完整性
            required_fields = ['X', 'c', 'S', 'ps']
            if not all(field in order_data for field in required_fields):
                logger.warning(f"订单数据缺少必要字段: {order_data}")
                return

            async with self.lock:
                try:
                    status = order_data['X']
                    client_order_id = order_data['c']

                    if status in ["CANCELED", "EXPIRED", "REJECTED"]:
                        if client_order_id in self.open_orders: 
                            del self.open_orders[client_order_id]
                        logger.info(f"订单终结 ({status}): ID: {client_order_id}")

                    elif status in ["FILLED", "PARTIALLY_FILLED"]:
                        try:
                            side = order_data['S']
                            position_side = order_data['ps']
                            filled_qty = float(order_data.get('l', 0))
                            filled_price = float(order_data.get('L', 0))
                            
                            # 验证成交数据有效性
                            if filled_qty <= 0 or filled_price <= 0:
                                logger.warning(f"无效的成交数据: qty={filled_qty}, price={filled_price}")
                                return
                            
                            logger.info(f"订单成交: {side} {position_side} {filled_qty:.4f} @ {filled_price:.4f}")

                            # 使用Binance提供的rp字段（已实现盈亏）来准确计算利润
                            # 这避免了复杂的价格匹配逻辑，特别是在多层网格中的准确性问题
                            try:
                                realized_pnl = order_data.get('rp')
                                if realized_pnl is not None:
                                    realized_pnl_value = float(realized_pnl)
                                    if realized_pnl_value != 0:  # 只有当实际有盈亏时才记录
                                        self.total_profit_usd += realized_pnl_value
                                        logger.info(f"交易已实现盈亏: {realized_pnl_value:.4f} USD, 累计利润: {self.total_profit_usd:.2f} USD")
                                    else:
                                        logger.debug(f"交易无盈亏变化: rp={realized_pnl}")
                                else:
                                    logger.debug("订单数据中未包含rp字段")
                                
                                # 重置连续亏损计数（无论是否有盈亏）
                                self.consecutive_losses = 0
                                
                            except (ValueError, TypeError) as e:
                                logger.error(f"处理已实现盈亏失败: {e}, rp值: {order_data.get('rp')}")
                                # 即使处理失败也重置连续亏损计数
                                self.consecutive_losses = 0

                            if status == "FILLED" and client_order_id in self.open_orders:
                                del self.open_orders[client_order_id]
                            
                            # 成交后立即同步状态，确保持仓准确
                            try:
                                asyncio.create_task(self.full_state_sync())
                            except Exception as sync_e:
                                logger.error(f"创建状态同步任务失败: {sync_e}")
                                
                        except (ValueError, TypeError, KeyError) as e:
                            logger.error(f"处理成交数据失败: {e}, 订单数据: {order_data}")
                            
                except KeyError as e:
                    logger.error(f"订单数据字段缺失: {e}, 数据: {order_data}")
                except Exception as e:
                    logger.error(f"处理订单更新时发生错误: {e}")
                    
        except Exception as e:
            logger.error(f"处理订单更新时发生未预期错误: {e}")
            logger.error(f"错误类型: {type(e).__name__}")
            logger.debug(f"订单数据: {data}")

    async def place_order(self, side, position_side, amount, price):
        """封装统一的下单请求"""
        try:
            async with self.order_semaphore:  # 使用信号量控制并发下单
                # 参数验证
                if not self.market:
                    logger.warning("市场信息未加载，无法下单")
                    return

                if not all([side, position_side, amount, price]):
                    logger.warning(f"下单参数不完整: side={side}, position_side={position_side}, amount={amount}, price={price}")
                    return

                # 检查交易对状态
                if not self.market.get('active', True):
                    logger.warning(f"交易对 {self.symbol} 当前不可交易")
                    return

                # 全面的数量和价格限制检查
                limits = self.market.get('limits', {})
                amount_limits = limits.get('amount', {})
                price_limits = limits.get('price', {})

                min_amount = amount_limits.get('min', 0)
                max_amount = amount_limits.get('max', float('inf'))
                min_price = price_limits.get('min', 0)
                max_price = price_limits.get('max', float('inf'))

                if amount < min_amount:
                    logger.warning(f"下单量 {amount:.4f} 小于最小限制 {min_amount}")
                    return
                if amount > max_amount:
                    logger.warning(f"下单量 {amount:.4f} 超过最大限制 {max_amount}")
                    return
                if price < min_price:
                    logger.warning(f"下单价格 {price:.4f} 低于最小限制 {min_price}")
                    return
                if price > max_price:
                    logger.warning(f"下单价格 {price:.4f} 超过最大限制 {max_price}")
                    return

                # 使用ccxt内置精度函数处理价格和数量
                try:
                    price = float(self.exchange.price_to_precision(self.symbol, price))
                    amount = float(self.exchange.amount_to_precision(self.symbol, amount))
                    logger.debug(f"精度处理后: price={price:.8f}, amount={amount:.8f}")
                except Exception as e:
                    logger.error(f"精度处理失败: {e}")
                    return

                # 生成唯一的客户端订单ID
                client_order_id = f"x-argm-{int(time.time() * 1000)}-{uuid.uuid4().hex[:4]}"
                params = {
                    'positionSide': position_side.upper(),
                    'newClientOrderId': client_order_id,
                    'timeInForce': 'GTC'
                }

                # 简化的重试机制
                max_retries = 2
                for attempt in range(max_retries + 1):
                    try:
                        logger.info(f"尝试下单 ({attempt + 1}/{max_retries + 1}): {side} {position_side} {amount:.4f} @ {price:.4f}")

                        new_order = await asyncio.wait_for(
                            self.exchange.create_order(self.symbol, 'limit', side, amount, price, params),
                            timeout=30
                        )

                        if new_order and 'clientOrderId' in new_order:
                            self.open_orders[new_order['clientOrderId']] = new_order
                            logger.info(f"下单成功: {new_order['clientOrderId']}")
                            return new_order
                        else:
                            logger.error(f"下单失败，返回数据异常: {new_order}")
                            return None

                    except asyncio.TimeoutError:
                        logger.warning(f"下单超时 (尝试 {attempt + 1}/{max_retries + 1})")
                        if attempt < max_retries:
                            await asyncio.sleep(2)
                            continue
                        return None

                    except Exception as e:
                        error_msg = str(e).lower()

                        # 不可重试的错误
                        if any(err in error_msg for err in ['insufficient', 'balance', 'margin', 'price', 'precision', 'filter']):
                            logger.error(f"下单失败（不可重试）: {e}")
                            return None

                        # 可重试的错误
                        elif any(err in error_msg for err in ['rate limit', 'network', 'connection', 'timeout']):
                            logger.warning(f"下单失败（可重试）: {e}")
                            if attempt < max_retries:
                                delay = 2 ** attempt  # 指数退避
                                await asyncio.sleep(delay)
                                continue
                            return None

                        else:
                            logger.error(f"下单失败（未知错误）: {e}")
                            if attempt < max_retries:
                                await asyncio.sleep(1)
                                continue
                            return None

        except asyncio.CancelledError:
            logger.warning(f"下单任务被取消: {side} {position_side} {amount} @ {price}")
            raise
        except Exception as e:
            logger.error(f"下单过程中发生未预期错误: {e}")
            return None

    async def cancel_order(self, client_order_id):
        """根据客户端订单ID取消订单"""
        try:
            # 添加超时保护，防止函数卡死
            async def _cancel_order_impl():
                # 检查订单是否存在
                order_to_cancel = self.open_orders.get(client_order_id)
                if not order_to_cancel:
                    logger.debug(f"订单不存在于本地记录中: {client_order_id}")
                    return

                exchange_order_id = order_to_cancel.get('id')
                if not exchange_order_id:
                    logger.warning(f"订单缺少交易所ID: {client_order_id}")
                    # 从本地记录中移除无效订单
                    if client_order_id in self.open_orders:
                        del self.open_orders[client_order_id]
                    return

                return exchange_order_id

            # 使用超时保护整个取消订单过程
            exchange_order_id = await asyncio.wait_for(_cancel_order_impl(), timeout=5)
            if not exchange_order_id:
                return

            max_retries = 3
            for attempt in range(max_retries):
                try:
                    logger.debug(f"准备撤销订单: ClientID={client_order_id}, ExchangeID={exchange_order_id} (尝试 {attempt + 1}/{max_retries})")

                    await asyncio.wait_for(
                        self.exchange.cancel_order(str(exchange_order_id), self.symbol),
                        timeout=10  # 减少超时时间到10秒
                    )

                    logger.debug(f"订单撤销成功: ClientID={client_order_id}")
                    # 成功撤销后从本地记录中移除
                    if client_order_id in self.open_orders:
                        del self.open_orders[client_order_id]
                    return

                except asyncio.TimeoutError:
                    logger.warning(f"撤单超时 (尝试 {attempt + 1}/{max_retries}): {client_order_id}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(1)
                        continue
                    else:
                        # 最后一次尝试失败，直接从本地移除
                        logger.warning(f"撤单超时，从本地移除订单: {client_order_id}")
                        if client_order_id in self.open_orders:
                            del self.open_orders[client_order_id]
                        return

                except Exception as e:
                    error_msg = str(e).lower()

                    # 处理订单已不存在的情况
                    if any(err in error_msg for err in ["order does not exist", "already filled", "unknown order", "order not found"]):
                        logger.debug(f"订单已不存在或已成交: {client_order_id}")
                        if client_order_id in self.open_orders:
                            del self.open_orders[client_order_id]
                        return

                    # 处理API限速
                    elif 'rate limit' in error_msg:
                        logger.warning(f"撤单API限速 (尝试 {attempt + 1}/{max_retries}): {e}")
                        if attempt < max_retries - 1:
                            await asyncio.sleep(2 ** attempt)  # 指数退避
                            continue
                        else:
                            # API限速导致的最终失败，也从本地移除
                            logger.warning(f"撤单API限速失败，从本地移除订单: {client_order_id}")
                            if client_order_id in self.open_orders:
                                del self.open_orders[client_order_id]
                            return

                    # 其他错误
                    else:
                        logger.error(f"撤单失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                        if attempt < max_retries - 1:
                            await asyncio.sleep(1)
                            continue
                        else:
                            # 其他错误导致的最终失败，也从本地移除避免状态不一致
                            logger.warning(f"撤单失败，从本地移除订单: {client_order_id}")
                            if client_order_id in self.open_orders:
                                del self.open_orders[client_order_id]
                            return

        except asyncio.TimeoutError:
            logger.error(f"撤单过程整体超时: {client_order_id}")
            # 超时也要从本地移除，避免状态不一致
            if client_order_id in self.open_orders:
                del self.open_orders[client_order_id]
        except Exception as e:
            logger.error(f"撤单过程中发生未预期错误: {e}")
            logger.error(f"错误类型: {type(e).__name__}")
            logger.debug(f"ClientOrderID: {client_order_id}")
            # 发生异常也要从本地移除，避免状态不一致
            if client_order_id in self.open_orders:
                del self.open_orders[client_order_id]
    
    async def cancel_all_open_orders(self):
        """取消所有挂单"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                logger.debug(f"开始取消所有挂单 (尝试 {attempt + 1}/{max_retries})")
                
                # 只有在正常运行状态下才同步状态
                if not self.is_shutting_down and hasattr(self, 'market') and self.market:
                    try:
                        await asyncio.wait_for(self.full_state_sync(), timeout=15)
                    except asyncio.TimeoutError:
                        logger.warning("状态同步超时，直接尝试取消挂单")
                    except Exception as e:
                        logger.warning(f"状态同步失败，直接尝试取消挂单: {e}")
                
                # 尝试取消所有挂单
                if self.open_orders:
                    logger.info(f"正在取消 {len(self.open_orders)} 个挂单...")
                else:
                    logger.info("正在尝试取消所有挂单...")
                
                # 使用超时控制取消操作
                await asyncio.wait_for(
                    self.exchange.cancel_all_orders(self.symbol),
                    timeout=30
                )
                
                # 清空本地订单缓存
                self.open_orders.clear()
                logger.info("所有挂单已取消")
                return  # 成功完成
                
            except asyncio.TimeoutError:
                logger.warning(f"取消挂单超时 (尝试 {attempt + 1}/{max_retries})")
                if attempt < max_retries - 1:
                    await asyncio.sleep(2)
                    continue
                    
            except Exception as e:
                error_msg = str(e).lower()
                
                # 检查是否是无挂单的情况
                if any(keyword in error_msg for keyword in ['no open orders', 'no orders to cancel', 'no such order']):
                    logger.info("没有需要取消的挂单")
                    self.open_orders.clear()
                    return
                
                # 检查是否是API限速
                if 'rate limit' in error_msg:
                    logger.warning(f"取消挂单API限速 (尝试 {attempt + 1}/{max_retries}): {e}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(3 * (attempt + 1))
                        continue
                else:
                    logger.error(f"取消挂单失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(1)
                        continue
                        
        logger.error("所有取消挂单尝试均失败")
        # 即使失败也清空本地缓存，避免状态不一致
        self.open_orders.clear()

    async def _internal_state_sync(self):
        """内部状态同步逻辑，不使用锁（避免死锁）"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                logger.debug(f"开始状态同步 (尝试 {attempt + 1}/{max_retries})")
                
                # 使用超时控制每个API调用
                timeout_duration = 30  # 30秒超时
                
                positions_f = asyncio.wait_for(
                    self.exchange.fetch_positions([self.symbol]),
                    timeout=timeout_duration
                )
                orders_f = asyncio.wait_for(
                    self.exchange.fetch_open_orders(self.symbol),
                    timeout=timeout_duration
                )
                ticker_f = asyncio.wait_for(
                    self.exchange.fetch_ticker(self.symbol),
                    timeout=timeout_duration
                )
                
                # 并发执行所有API调用
                positions, open_orders, ticker = await asyncio.gather(
                    positions_f, orders_f, ticker_f,
                    return_exceptions=True
                )
                
                # 检查是否有异常
                if isinstance(positions, Exception):
                    logger.error(f"获取持仓失败: {positions}")
                    raise positions
                if isinstance(open_orders, Exception):
                    logger.error(f"获取挂单失败: {open_orders}")
                    raise open_orders
                if isinstance(ticker, Exception):
                    logger.error(f"获取ticker失败: {ticker}")
                    raise ticker

                # 更新价格信息
                if ticker and isinstance(ticker, dict) and 'bid' in ticker and ticker['bid'] and 'ask' in ticker and ticker['ask']:
                    self.best_bid_price = float(ticker['bid'])
                    self.best_ask_price = float(ticker['ask'])
                    self.last_price = (self.best_bid_price + self.best_ask_price) / 2
                    logger.debug(f"价格已更新: bid={self.best_bid_price}, ask={self.best_ask_price}, last={self.last_price}")
                elif ticker and isinstance(ticker, dict) and 'last' in ticker and ticker['last']:
                    self.last_price = float(ticker['last'])
                    logger.warning(f"Ticker数据缺少bid/ask，仅使用last价格更新: {self.last_price}")
                else:
                    logger.warning("Ticker数据无效或不完整，保持当前价格状态")

                # 更新持仓信息
                try:
                    if isinstance(positions, list):
                        long_pos = next((p for p in positions if p.get('info', {}).get('positionSide') == 'LONG'), None)
                        short_pos = next((p for p in positions if p.get('info', {}).get('positionSide') == 'SHORT'), None)
                    else:
                        logger.warning(f"持仓数据格式异常: {type(positions)}")
                        long_pos = short_pos = None
                    
                    long_contracts = long_pos.get('contracts', 0) if long_pos else 0
                    short_contracts = short_pos.get('contracts', 0) if short_pos else 0
                    
                    self.long_position_size = float(long_contracts) if long_contracts is not None else 0.0
                    self.short_position_size = float(short_contracts) if short_contracts is not None else 0.0
                    self.net_inventory = self.long_position_size - self.short_position_size
                    
                except Exception as e:
                    logger.warning(f"解析持仓数据失败: {e}")
                    # 保持现有持仓数据
                
                # 更新挂单信息 - 修复订单状态同步
                try:
                    # 验证并更新本地订单缓存
                    valid_orders = {}
                    if isinstance(open_orders, list):
                        for o in open_orders:
                            if (o.get('status') in ['open', 'new', 'partially_filled'] and 
                                o.get('symbol') == self.symbol and 
                                o.get('clientOrderId')):
                                valid_orders[o['clientOrderId']] = o
                    
                    self.open_orders = valid_orders
                    logger.info(f"同步到 {len(self.open_orders)} 个有效挂单")  # 添加日志确认同步结果
                        
                except Exception as e:
                    logger.error(f"解析挂单数据失败: {e}")
                    logger.debug(f"原始挂单数据: {open_orders}")
                    # 保持现有挂单数据
                
                self.last_state_sync_time = time.time()
                logger.debug(f"状态同步完成。持仓: L={self.long_position_size:.4f}, S={self.short_position_size:.4f}. 挂单: {len(self.open_orders)}")
                return  # 成功完成，退出重试循环
                
            except asyncio.TimeoutError:
                logger.warning(f"状态同步超时 (尝试 {attempt + 1}/{max_retries})")
                if attempt < max_retries - 1:
                    await asyncio.sleep(2)
                    continue
                    
            except Exception as e:
                error_msg = str(e).lower()
                if 'rate limit' in error_msg:
                    logger.warning(f"状态同步API限速 (尝试 {attempt + 1}/{max_retries}): {e}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(3 * (attempt + 1))  # 递增延迟
                        continue
                else:
                    logger.error(f"状态同步失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(1)
                        continue
                
        logger.error("所有状态同步尝试均失败")

    async def full_state_sync(self):
        """通过REST API完全同步持仓和挂单状态"""
        async with self.lock:
            await self._internal_state_sync()

    # --------------------------------------------------------------------------
    # --- 辅助函数 ---
    # --------------------------------------------------------------------------
    async def update_price_series(self):
        """更新用于计算指标的K线数据"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                cfg = self.config.get('argm', {})
                if not cfg:
                    logger.error("ARGM配置缺失")
                    return
                
                # 获取所需的周期参数
                periods_needed = ['ema_trend_period', 'ema_anchor_period', 'atr_period', 'rsi_period']
                periods = []
                for p in periods_needed:
                    if p in cfg and isinstance(cfg[p], (int, float)) and cfg[p] > 0:
                        periods.append(cfg[p])
                    else:
                        logger.warning(f"配置参数 {p} 无效或缺失")
                
                if not periods:
                    logger.error("没有有效的周期参数")
                    return
                
                limit = max(periods) + 10  # 增加缓冲
                timeframe = cfg.get('kline_timeframe', '1m')
                
                logger.debug(f"获取K线数据 (尝试 {attempt + 1}/{max_retries}): {timeframe}, limit={limit}")
                
                # 使用超时控制
                ohlcv = await asyncio.wait_for(
                    self.exchange.fetch_ohlcv(self.symbol, timeframe, limit=limit),
                    timeout=30
                )
                
                # 验证数据
                if not ohlcv or len(ohlcv) < max(periods):
                    raise ValueError(f"K线数据不足: 需要{max(periods)}条，获得{len(ohlcv) if ohlcv else 0}条")
                
                # 创建DataFrame并验证
                df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                
                # 检查数据完整性
                if df.isnull().any().any():
                    logger.warning("K线数据包含空值，尝试清理")
                    df = df.dropna()
                    
                if len(df) < max(periods):
                    raise ValueError(f"清理后K线数据不足: 需要{max(periods)}条，剩余{len(df)}条")
                
                # 检查价格数据合理性
                for col in ['open', 'high', 'low', 'close']:
                    if (df[col] <= 0).any():
                        raise ValueError(f"发现无效的{col}价格数据")
                
                self.price_series = df
                logger.debug(f"K线数据已更新，共 {len(self.price_series)} 条记录")
                return  # 成功完成
                
            except asyncio.TimeoutError:
                logger.warning(f"获取K线数据超时 (尝试 {attempt + 1}/{max_retries})")
                if attempt < max_retries - 1:
                    await asyncio.sleep(2)
                    continue
                    
            except Exception as e:
                error_msg = str(e).lower()
                if 'rate limit' in error_msg:
                    logger.warning(f"获取K线数据API限速 (尝试 {attempt + 1}/{max_retries}): {e}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(3 * (attempt + 1))
                        continue
                else:
                    logger.error(f"更新K线数据失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(1)
                        continue
                        
        logger.error("所有更新K线数据尝试均失败")

    def generate_nonlinear_grids(self, center, low, high, num_levels):
        """非线性网格生成"""
        try:
            # 参数验证
            if not all(isinstance(x, (int, float)) for x in [center, low, high, num_levels]):
                logger.error("网格生成参数类型错误")
                return []
            
            if high <= low:
                logger.warning(f"网格范围无效: high({high}) <= low({low})")
                return []
            
            if num_levels <= 0:
                logger.warning(f"网格层数无效: {num_levels}")
                return []
            
            if center < low or center > high:
                logger.warning(f"中心价格({center})超出范围[{low}, {high}]")
                center = (low + high) / 2  # 使用中点作为中心
            
            # 限制网格数量，避免过多
            num_levels = min(num_levels, 100)
            
            # 使用sigmoid函数创建在中心密集的点
            points = np.linspace(-5, 5, num_levels)
            sigmoid_points = 1 / (1 + np.exp(-points))
            
            # 将sigmoid输出 [0,1] 缩放到 [low, high]
            prices = low + (high - low) * sigmoid_points
            
            # 获取交易所价格精度
            try:
                if self.market and 'precision' in self.market and 'price' in self.market['precision']:
                    price_precision = self.market['precision']['price']
                    if price_precision is not None and price_precision > 0:
                        decimals = int(-math.log10(price_precision))
                    else:
                        decimals = 4
                else:
                    decimals = 4
            except Exception as e:
                logger.warning(f"获取价格精度失败，使用默认值: {e}")
                decimals = 4
            
            # 确保decimals在合理范围内
            decimals = max(0, min(decimals, 8))
            
            # 生成网格价格并验证精度
            rounded_prices = np.round(prices, decimals)
            unique_prices = sorted(list(set(rounded_prices)))
            
            # 验证生成的网格
            if len(unique_prices) < 2:
                logger.warning(f"生成的网格点太少: {len(unique_prices)}")
                return []
            
            # 检查价格是否在合理范围内
            valid_prices = [p for p in unique_prices if low <= p <= high and p > 0]

            if len(valid_prices) != len(unique_prices):
                logger.warning(f"过滤无效价格: {len(unique_prices)} -> {len(valid_prices)}")

            # 使用 price_to_precision 格式化价格，确保符合交易所精度要求
            try:
                formatted_prices = [float(self.exchange.price_to_precision(self.symbol, p)) for p in valid_prices]
            except Exception as e:
                logger.error(f"价格精度格式化失败: {e}")
                formatted_prices = valid_prices  # 回退到原始价格

            # 去重并过滤无效价格
            final_prices = sorted(list(set([p for p in formatted_prices if p > 0])))

            logger.debug(f"生成网格: {len(final_prices)}个价格点")
            return final_prices
            
        except Exception as e:
            logger.error(f"网格生成失败: {e}")
            return []

    def calculate_grid_overlap(self, new_grids, old_grids):
        """计算新旧网格重合度"""
        if not old_grids or not new_grids: return 0.0
        old_set = set(old_grids)
        new_set = set(new_grids)
        return len(old_set.intersection(new_set)) / len(old_set)

    async def set_hedge_mode(self):
        """设置账户为双向持仓模式"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                logger.debug(f"设置双向持仓模式 (尝试 {attempt + 1}/{max_retries})")
                
                await asyncio.wait_for(
                    self.exchange.fapiPrivatePostPositionSideDual({"dualSidePosition": "true"}),
                    timeout=15
                )
                logger.info("双向持仓模式已成功设置")
                return
                
            except asyncio.TimeoutError:
                logger.warning(f"设置双向持仓模式超时 (尝试 {attempt + 1}/{max_retries})")
                if attempt < max_retries - 1:
                    await asyncio.sleep(2)
                    continue
                    
            except Exception as e:
                error_msg = str(e).lower()
                
                # 检查是否已经是双向持仓模式
                if any(err in error_msg for err in ["no need to change", "-4059", "already"]):
                    logger.info("当前已是双向持仓模式，无需更改")
                    return
                
                # 检查是否是API限速
                if 'rate limit' in error_msg:
                    logger.warning(f"设置双向持仓模式API限速 (尝试 {attempt + 1}/{max_retries}): {e}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(3 * (attempt + 1))
                        continue
                else:
                    logger.error(f"设置双向持仓模式失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(1)
                        continue
                        
        logger.error("所有设置双向持仓模式尝试均失败")

    async def set_leverage(self):
        """设置杠杆倍数"""
        max_retries = 3
        leverage = self.config.get('leverage', 1)
        
        # 验证杠杆倍数
        if not isinstance(leverage, (int, float)) or leverage <= 0 or leverage > 125:
            logger.error(f"杠杆倍数无效: {leverage}")
            return
            
        for attempt in range(max_retries):
            try:
                logger.debug(f"设置杠杆倍数 (尝试 {attempt + 1}/{max_retries}): {leverage}x")
                
                await asyncio.wait_for(
                    self.exchange.set_leverage(leverage, self.symbol),
                    timeout=15
                )
                logger.info(f"杠杆已设置为 {leverage}x")
                return
                
            except asyncio.TimeoutError:
                logger.warning(f"设置杠杆超时 (尝试 {attempt + 1}/{max_retries})")
                if attempt < max_retries - 1:
                    await asyncio.sleep(2)
                    continue
                    
            except Exception as e:
                error_msg = str(e).lower()
                
                # 检查是否杠杆已经设置
                if any(keyword in error_msg for keyword in ['leverage not modified', 'no need to change', 'already']):
                    logger.info(f"杠杆已经是 {leverage}x，无需更改")
                    return
                
                # 检查是否是API限速
                if 'rate limit' in error_msg:
                    logger.warning(f"设置杠杆API限速 (尝试 {attempt + 1}/{max_retries}): {e}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(3 * (attempt + 1))
                        continue
                else:
                    logger.error(f"设置杠杆失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(1)
                        continue
                        
        logger.error("所有设置杠杆尝试均失败")

    async def fetch_listen_key(self):
        """获取WebSocket监听密钥"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                logger.debug(f"获取Listen Key (尝试 {attempt + 1}/{max_retries})")
                
                response = await asyncio.wait_for(
                    self.exchange.fapiPrivatePostListenKey({}),
                    timeout=15
                )
                
                if not isinstance(response, dict):
                    raise ValueError(f"Listen Key响应格式错误: {type(response)}")
                
                key = response.get('listenKey')
                if not key or not isinstance(key, str):
                    raise ValueError(f"获取的 listenKey 无效: {key}")
                
                # 验证key格式（通常是64位字符串）
                if len(key) < 32:
                    raise ValueError(f"Listen Key长度异常: {len(key)}")
                
                logger.info(f"成功获取新的 listen key: {key[:10]}...")
                return key
                
            except asyncio.TimeoutError:
                logger.warning(f"获取Listen Key超时 (尝试 {attempt + 1}/{max_retries})")
                if attempt < max_retries - 1:
                    await asyncio.sleep(2)
                    continue
                    
            except Exception as e:
                error_msg = str(e).lower()
                
                if 'rate limit' in error_msg:
                    logger.warning(f"获取Listen Key API限速 (尝试 {attempt + 1}/{max_retries}): {e}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(3 * (attempt + 1))
                        continue
                else:
                    logger.error(f"获取Listen Key失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(1)
                        continue
                        
        logger.error("所有获取Listen Key尝试均失败")
        raise Exception("无法获取Listen Key")

    async def keep_listen_key_alive(self):
        """后台任务，定期延长 listenKey 的有效期"""
        while not self.is_shutting_down:
            try:
                await asyncio.sleep(1800)  # 每30分钟
                
                if self.is_shutting_down:
                    break
                    
                if not hasattr(self, 'listen_key') or not self.listen_key:
                    logger.warning("Listen Key不存在，跳过延期")
                    continue
                
                logger.debug("延长Listen Key有效期")
                
                # 延长listen key有效期
                await asyncio.wait_for(
                    self.exchange.fapiPrivatePutListenKey({}),
                    timeout=15
                )
                
                logger.debug("Listen Key有效期已延长")
                
            except asyncio.TimeoutError:
                logger.warning("延长Listen Key超时")
                
            except Exception as e:
                error_msg = str(e).lower()
                if 'rate limit' in error_msg:
                    logger.warning(f"延长Listen Key API限速: {e}")
                    await asyncio.sleep(60)  # 限速时等待更长时间
                else:
                    logger.error(f"延长Listen Key失败: {e}")
                    # 尝试重新获取listen key
                    try:
                        self.listen_key = await self.fetch_listen_key()
                        logger.info("已重新获取Listen Key")
                    except Exception as fetch_error:
                        logger.error(f"重新获取Listen Key失败: {fetch_error}")
                        await asyncio.sleep(300)  # 失败时等待5分钟再试
        
        logger.info("Listen Key保活任务已停止")


# ====================================================================================
# --- 主程序入口 ---
# ====================================================================================
async def main():
    """主程序入口函数"""
    bot = None
    try:
        # 参考 maker_BN_new.py, 增加启动前的API连接预检
        logger.info("启动前预检 API 连接...")
        proxy = CONFIG.get('proxy_url') if CONFIG.get('use_proxy') else None
        ping_url = "https://fapi.binance.com/fapi/v1/ping"
        
        # 检查代理是否可用
        if proxy:
            logger.info(f"检测到代理配置: {proxy}")
            try:
                # 先测试代理本身是否可用
                connector = aiohttp.TCPConnector(
                    limit=10,
                    limit_per_host=5,
                    ttl_dns_cache=300,
                    use_dns_cache=True,
                    keepalive_timeout=30,
                    enable_cleanup_closed=True
                )
                
                timeout = aiohttp.ClientTimeout(
                    total=30,
                    connect=15,
                    sock_read=15
                )
                
                async with aiohttp.ClientSession(
                    connector=connector,
                    timeout=timeout,
                    headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
                ) as session:
                    # 使用更可靠的测试URL
                    test_urls = [
                        "http://httpbin.org/ip", 
                        "https://api.binance.com/api/v3/ping",
                        "http://www.google.com"
                    ]
                    proxy_working = False
                    
                    for test_url in test_urls:
                        try:
                            logger.info(f"测试代理连接: {test_url}")
                            async with session.get(test_url, proxy=proxy) as response:
                                if response.status == 200:
                                    logger.info(f"代理服务器连接正常 (测试URL: {test_url})")
                                    proxy_working = True
                                    break
                                else:
                                    logger.warning(f"代理测试返回状态码: {response.status}")
                        except Exception as url_e:
                            logger.warning(f"代理测试URL {test_url} 失败: {url_e}")
                            continue

                    if not proxy_working:
                        logger.warning("代理服务器测试失败，但继续尝试运行...")
                        logger.warning("如果后续连接失败，请检查代理服务器配置")
                    else:
                        logger.info("代理服务器测试通过")

            except Exception as e:
                logger.error(f"代理服务器连接测试失败: {e}")
                logger.error("请检查代理服务器是否正在运行且配置正确")
                logger.warning("程序将继续运行，但可能会遇到网络连接问题...")
                # 不要直接返回，让程序继续尝试
        
        # 测试API端点
        max_retries = 5
        api_test_passed = False
        
        for attempt in range(max_retries):
            try:
                # 使用优化的连接配置
                connector = aiohttp.TCPConnector(
                    limit=20,
                    limit_per_host=10,
                    ttl_dns_cache=300,
                    use_dns_cache=True,
                    keepalive_timeout=30,
                    enable_cleanup_closed=True
                )
                
                timeout = aiohttp.ClientTimeout(
                    total=60,  # 增加总超时时间
                    connect=30,  # 连接超时
                    sock_read=30  # 读取超时
                )
                
                async with aiohttp.ClientSession(
                    connector=connector,
                    timeout=timeout,
                    headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
                ) as session:
                    logger.info(f"正在测试 API 端点: {ping_url} (代理: {proxy or '无'}) - 尝试 {attempt + 1}/{max_retries}")
                    start_time = time.time()
                    
                    async with session.get(ping_url, proxy=proxy) as response:
                        elapsed = time.time() - start_time
                        response_text = await response.text()
                        
                        if response.status == 200:
                            logger.info(f"API 端点预检成功, 响应码: {response.status}, 耗时: {elapsed:.2f}s")
                            logger.debug(f"响应内容: {response_text[:100]}...")
                            api_test_passed = True
                            break
                        else:
                            logger.warning(f"API 端点返回状态码: {response.status}, 耗时: {elapsed:.2f}s")
                            logger.debug(f"响应内容: {response_text[:200]}...")
                            
                            if attempt == max_retries - 1:
                                logger.warning("API端点返回非200状态码，但继续尝试运行...")
                                
            except asyncio.TimeoutError:
                logger.warning(f"API 连接超时 (尝试 {attempt + 1}/{max_retries})")
                if attempt < max_retries - 1:
                    logger.info("等待3秒后重试...")
                    await asyncio.sleep(3)
                    continue
            except aiohttp.ClientError as e:
                logger.warning(f"API 客户端错误 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    logger.info("等待3秒后重试...")
                    await asyncio.sleep(3)
                    continue
            except Exception as e:
                logger.error(f"API 连接预检失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                logger.error(f"错误类型: {type(e).__name__}")
                if attempt < max_retries - 1:
                    logger.info("等待5秒后重试...")
                    await asyncio.sleep(5)
                    continue
                else:
                    logger.warning("所有API连接预检尝试均失败，但继续尝试运行...")
                    if proxy:
                        logger.warning("请检查代理服务器是否正在运行且配置正确。")
                    else:
                        logger.warning("请检查您的网络连接。")
                    # 不要直接退出，让程序继续尝试

        if not api_test_passed:
            logger.warning("API预检失败，但程序将继续运行。如果后续连接失败，请检查网络和代理设置。")

        logger.info("启动 ARGM-V6.0 'Singularity' 策略...")
        bot = ARGMStrategyBot(CONFIG)
        await bot.run()
    except KeyboardInterrupt:
        logger.info("检测到用户中断 (Ctrl+C)...")
    except Exception as e:
        logger.critical(f"程序顶层发生未捕获的严重错误: {e}", exc_info=True)
    finally:
        if bot:
            await bot.close()
    logger.info("程序已完全退出。")

if __name__ == "__main__":
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    asyncio.run(main())