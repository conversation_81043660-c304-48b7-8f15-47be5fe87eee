2025-07-17 01:43:07,061 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 01:43:07,061 - INFO - [_initialize_exchange] - 已设置代理: http://127.0.0.1:7897
2025-07-17 01:43:07,070 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 01:43:07,070 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 01:43:07,072 - INFO - [setup] - 正在执行启动设置...
2025-07-17 01:43:07,072 - INFO - [test_connection] - 正在测试API连接...
2025-07-17 01:43:07,158 - INFO - [test_connection] - 交易所状态检查成功: {'status': 'ok', 'updated': None, 'eta': None, 'url': None, 'info': {'status': '0', 'msg': 'normal'}}
2025-07-17 01:43:07,822 - INFO - [test_connection] - 成功获取行情数据: 最新价格 172.33
2025-07-17 01:43:07,892 - INFO - [test_connection] - 成功获取账户余额: USDC = 611.43812764
2025-07-17 01:43:07,892 - INFO - [test_connection] - 所有API测试通过
2025-07-17 01:43:07,892 - INFO - [setup] - 正在加载市场数据...
2025-07-17 01:43:08,475 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-17 01:43:08,542 - INFO - [set_hedge_mode] - 当前已是双向持仓模式，无需更改。
2025-07-17 01:43:08,606 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-17 01:43:08,872 - INFO - [fetch_listen_key] - 正在获取新的Listen Key...
2025-07-17 01:43:08,942 - INFO - [fetch_listen_key] - 成功获取新的 listen key: zQY0aCQW39...
2025-07-17 01:43:08,942 - INFO - [setup] - 初始化设置完成。
2025-07-17 01:43:08,942 - INFO - [setup] - 当前持仓: 多头=0.0, 空头=0.0
2025-07-17 01:43:08,942 - ERROR - [cancel_all_open_orders] - 批量撤单失败: 'ARGMStrategyBot' object has no attribute 'sync_order_status'
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 533, in cancel_all_open_orders
    await self.sync_order_status()
          ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ARGMStrategyBot' object has no attribute 'sync_order_status'
2025-07-17 01:43:08,944 - INFO - [run] - 所有现有挂单已取消，准备启动策略...
2025-07-17 01:43:08,944 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/zQY0aCQW39lN0Pg2uFTanCpwPLgZqnQwV5Dgu4jQP9fPJ2hqX0vIhFP7rhh7d1R5
2025-07-17 01:43:09,142 - INFO - [run] - WebSocket 连接成功。
2025-07-17 01:43:09,143 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdc@bookTicker'], 'id': 1}
2025-07-17 01:43:09,362 - ERROR - [generate_grid_state] - 生成网格状态失败: 'float' object cannot be interpreted as an integer
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 57, in _wrapfunc
    return bound(*args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 378, in generate_grid_state
    new_grids = self.generate_nonlinear_grids(center=anchor, low=grid_low, high=grid_high, num_levels=cfg['grid_total_levels'])
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 593, in generate_nonlinear_grids
    return sorted(list(set(np.round(prices, self.market['precision']['price']))))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 3758, in round
    return _wrapfunc(a, 'round', decimals=decimals, out=out)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 66, in _wrapfunc
    return _wrapit(obj, method, *args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 46, in _wrapit
    result = getattr(arr, method)(*args, **kwds)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer
2025-07-17 01:43:09,959 - ERROR - [generate_grid_state] - 生成网格状态失败: 'float' object cannot be interpreted as an integer
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 57, in _wrapfunc
    return bound(*args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 378, in generate_grid_state
    new_grids = self.generate_nonlinear_grids(center=anchor, low=grid_low, high=grid_high, num_levels=cfg['grid_total_levels'])
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 593, in generate_nonlinear_grids
    return sorted(list(set(np.round(prices, self.market['precision']['price']))))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 3758, in round
    return _wrapfunc(a, 'round', decimals=decimals, out=out)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 66, in _wrapfunc
    return _wrapit(obj, method, *args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 46, in _wrapit
    result = getattr(arr, method)(*args, **kwds)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer
2025-07-17 01:43:10,466 - ERROR - [generate_grid_state] - 生成网格状态失败: 'float' object cannot be interpreted as an integer
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 57, in _wrapfunc
    return bound(*args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 378, in generate_grid_state
    new_grids = self.generate_nonlinear_grids(center=anchor, low=grid_low, high=grid_high, num_levels=cfg['grid_total_levels'])
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 593, in generate_nonlinear_grids
    return sorted(list(set(np.round(prices, self.market['precision']['price']))))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 3758, in round
    return _wrapfunc(a, 'round', decimals=decimals, out=out)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 66, in _wrapfunc
    return _wrapit(obj, method, *args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 46, in _wrapit
    result = getattr(arr, method)(*args, **kwds)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer
2025-07-17 01:43:10,972 - ERROR - [generate_grid_state] - 生成网格状态失败: 'float' object cannot be interpreted as an integer
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 57, in _wrapfunc
    return bound(*args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 378, in generate_grid_state
    new_grids = self.generate_nonlinear_grids(center=anchor, low=grid_low, high=grid_high, num_levels=cfg['grid_total_levels'])
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 593, in generate_nonlinear_grids
    return sorted(list(set(np.round(prices, self.market['precision']['price']))))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 3758, in round
    return _wrapfunc(a, 'round', decimals=decimals, out=out)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 66, in _wrapfunc
    return _wrapit(obj, method, *args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 46, in _wrapit
    result = getattr(arr, method)(*args, **kwds)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer
2025-07-17 01:43:11,782 - ERROR - [generate_grid_state] - 生成网格状态失败: 'float' object cannot be interpreted as an integer
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 57, in _wrapfunc
    return bound(*args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 378, in generate_grid_state
    new_grids = self.generate_nonlinear_grids(center=anchor, low=grid_low, high=grid_high, num_levels=cfg['grid_total_levels'])
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 593, in generate_nonlinear_grids
    return sorted(list(set(np.round(prices, self.market['precision']['price']))))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 3758, in round
    return _wrapfunc(a, 'round', decimals=decimals, out=out)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 66, in _wrapfunc
    return _wrapit(obj, method, *args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 46, in _wrapit
    result = getattr(arr, method)(*args, **kwds)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer
2025-07-17 01:43:12,295 - ERROR - [generate_grid_state] - 生成网格状态失败: 'float' object cannot be interpreted as an integer
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 57, in _wrapfunc
    return bound(*args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 378, in generate_grid_state
    new_grids = self.generate_nonlinear_grids(center=anchor, low=grid_low, high=grid_high, num_levels=cfg['grid_total_levels'])
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 593, in generate_nonlinear_grids
    return sorted(list(set(np.round(prices, self.market['precision']['price']))))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 3758, in round
    return _wrapfunc(a, 'round', decimals=decimals, out=out)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 66, in _wrapfunc
    return _wrapit(obj, method, *args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 46, in _wrapit
    result = getattr(arr, method)(*args, **kwds)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer
2025-07-17 01:43:12,822 - ERROR - [generate_grid_state] - 生成网格状态失败: 'float' object cannot be interpreted as an integer
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 57, in _wrapfunc
    return bound(*args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 378, in generate_grid_state
    new_grids = self.generate_nonlinear_grids(center=anchor, low=grid_low, high=grid_high, num_levels=cfg['grid_total_levels'])
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 593, in generate_nonlinear_grids
    return sorted(list(set(np.round(prices, self.market['precision']['price']))))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 3758, in round
    return _wrapfunc(a, 'round', decimals=decimals, out=out)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 66, in _wrapfunc
    return _wrapit(obj, method, *args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 46, in _wrapit
    result = getattr(arr, method)(*args, **kwds)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer
2025-07-17 01:43:13,327 - ERROR - [generate_grid_state] - 生成网格状态失败: 'float' object cannot be interpreted as an integer
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 57, in _wrapfunc
    return bound(*args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 378, in generate_grid_state
    new_grids = self.generate_nonlinear_grids(center=anchor, low=grid_low, high=grid_high, num_levels=cfg['grid_total_levels'])
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 593, in generate_nonlinear_grids
    return sorted(list(set(np.round(prices, self.market['precision']['price']))))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 3758, in round
    return _wrapfunc(a, 'round', decimals=decimals, out=out)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 66, in _wrapfunc
    return _wrapit(obj, method, *args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 46, in _wrapit
    result = getattr(arr, method)(*args, **kwds)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer
2025-07-17 01:43:13,851 - ERROR - [generate_grid_state] - 生成网格状态失败: 'float' object cannot be interpreted as an integer
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 57, in _wrapfunc
    return bound(*args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 378, in generate_grid_state
    new_grids = self.generate_nonlinear_grids(center=anchor, low=grid_low, high=grid_high, num_levels=cfg['grid_total_levels'])
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 593, in generate_nonlinear_grids
    return sorted(list(set(np.round(prices, self.market['precision']['price']))))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 3758, in round
    return _wrapfunc(a, 'round', decimals=decimals, out=out)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 66, in _wrapfunc
    return _wrapit(obj, method, *args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 46, in _wrapit
    result = getattr(arr, method)(*args, **kwds)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer
2025-07-17 01:43:20,135 - INFO - [close] - 正在关闭程序...
2025-07-17 01:43:20,135 - ERROR - [cancel_all_open_orders] - 批量撤单失败: 'ARGMStrategyBot' object has no attribute 'sync_order_status'
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 657, in main
    await bot.run()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 274, in run
    message = await asyncio.wait_for(websocket.recv(), timeout=60)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\asyncio\tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\websockets\asyncio\connection.py", line 303, in recv
    return await self.recv_messages.get(decode)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\websockets\asyncio\messages.py", line 159, in get
    frame = await self.frames.get(not self.closed)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\websockets\asyncio\messages.py", line 51, in get
    await self.get_waiter
asyncio.exceptions.CancelledError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 533, in cancel_all_open_orders
    await self.sync_order_status()
          ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ARGMStrategyBot' object has no attribute 'sync_order_status'
2025-07-17 01:43:20,138 - INFO - [close] - 程序已关闭。
2025-07-17 01:44:52,124 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 01:44:52,124 - INFO - [_initialize_exchange] - 已设置代理: http://127.0.0.1:7897
2025-07-17 01:44:52,131 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 01:44:52,131 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 01:44:52,133 - INFO - [setup] - 正在执行启动设置...
2025-07-17 01:44:52,133 - INFO - [test_connection] - 正在测试API连接...
2025-07-17 01:44:52,173 - ERROR - [test_connection] - 连接测试失败: binanceusdm GET https://api.binance.com/sapi/v1/system/status
2025-07-17 01:44:52,173 - ERROR - [setup] - 初始化设置失败: API连接测试失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 225, in setup
    if not await self.test_connection(): raise Exception("API连接测试失败")
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: API连接测试失败
2025-07-17 01:44:52,174 - CRITICAL - [main] - 程序顶层发生未捕获的严重错误: API连接测试失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 665, in main
    await bot.run()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 253, in run
    await self.setup()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 225, in setup
    if not await self.test_connection(): raise Exception("API连接测试失败")
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: API连接测试失败
2025-07-17 01:44:52,174 - INFO - [close] - 正在关闭程序...
2025-07-17 01:44:52,999 - INFO - [close] - 程序已关闭。
2025-07-17 01:44:52,999 - INFO - [main] - 程序已完全退出。
2025-07-17 01:46:30,092 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 01:46:30,092 - INFO - [_initialize_exchange] - 已设置代理: http://127.0.0.1:7897
2025-07-17 01:46:30,099 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 01:46:30,099 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 01:46:30,101 - INFO - [setup] - 正在执行启动设置...
2025-07-17 01:46:30,101 - INFO - [test_connection] - 正在测试API连接...
2025-07-17 01:46:30,101 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 01:46:30,187 - INFO - [test_connection] - Futures API连通性检查成功。
2025-07-17 01:46:30,720 - INFO - [test_connection] - 成功获取行情数据: 最新价格 173.64
2025-07-17 01:46:30,783 - INFO - [test_connection] - 成功获取账户余额: USDC = 611.43812764
2025-07-17 01:46:30,783 - INFO - [test_connection] - 所有API测试通过
2025-07-17 01:46:30,783 - INFO - [setup] - 正在加载市场数据...
2025-07-17 01:46:31,262 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-17 01:46:31,324 - INFO - [set_hedge_mode] - 当前已是双向持仓模式，无需更改。
2025-07-17 01:46:31,388 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-17 01:46:31,654 - INFO - [fetch_listen_key] - 正在获取新的Listen Key...
2025-07-17 01:46:31,720 - INFO - [fetch_listen_key] - 成功获取新的 listen key: zQY0aCQW39...
2025-07-17 01:46:31,720 - INFO - [setup] - 初始化设置完成。
2025-07-17 01:46:31,720 - INFO - [setup] - 当前持仓: 多头=0.0, 空头=0.0
2025-07-17 01:46:31,836 - INFO - [run] - 所有现有挂单已取消，准备启动策略...
2025-07-17 01:46:31,836 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/zQY0aCQW39lN0Pg2uFTanCpwPLgZqnQwV5Dgu4jQP9fPJ2hqX0vIhFP7rhh7d1R5
2025-07-17 01:46:32,032 - INFO - [run] - WebSocket 连接成功。
2025-07-17 01:46:32,033 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdc@bookTicker'], 'id': 1}
2025-07-17 01:46:32,150 - INFO - [execute_argm_strategy] - 网格重绘触发！新中枢: 168.5762, 新范围: [166.2366, 170.9157]
2025-07-17 01:46:32,151 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 166.2700
2025-07-17 01:46:32,151 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 166.2800
2025-07-17 01:46:32,151 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 166.3000
2025-07-17 01:46:32,152 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 166.3200
2025-07-17 01:46:32,152 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 166.3600
2025-07-17 01:46:32,153 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 166.4100
2025-07-17 01:46:32,153 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 166.4700
2025-07-17 01:46:32,153 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 166.5600
2025-07-17 01:46:32,154 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 166.6900
2025-07-17 01:46:32,155 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 166.8500
2025-07-17 01:46:32,155 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 167.0500
2025-07-17 01:46:32,155 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 167.3100
2025-07-17 01:46:32,156 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 167.6300
2025-07-17 01:46:32,157 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 167.9800
2025-07-17 01:46:32,157 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 168.3700
2025-07-17 01:46:32,158 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 168.7800
2025-07-17 01:46:32,159 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 169.1700
2025-07-17 01:46:32,159 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 169.5300
2025-07-17 01:46:32,160 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 169.8400
2025-07-17 01:46:32,162 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 170.1000
2025-07-17 01:46:32,162 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 170.3100
2025-07-17 01:46:32,163 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 170.4700
2025-07-17 01:46:32,163 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 170.5900
2025-07-17 01:46:32,163 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 170.6800
2025-07-17 01:46:32,164 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 170.7500
2025-07-17 01:46:32,164 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 170.7900
2025-07-17 01:46:32,164 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 170.8300
2025-07-17 01:46:32,164 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 170.8500
2025-07-17 01:46:32,164 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 170.8700
2025-07-17 01:46:32,165 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 170.8800
2025-07-17 01:46:32,379 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:46:32,380 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:46:32,381 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:46:32,381 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:46:32,381 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:46:32,382 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:46:32,382 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:46:32,382 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:46:32,382 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:46:32,382 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:32,616 - INFO - [execute_argm_strategy] - 网格重绘触发！新中枢: 168.5867, 新范围: [166.0605, 171.1128]
2025-07-17 01:47:32,680 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992160-de46
2025-07-17 01:47:32,732 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992152-964e
2025-07-17 01:47:32,733 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992164-672d
2025-07-17 01:47:32,733 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992154-a7e7
2025-07-17 01:47:32,735 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992155-40b4
2025-07-17 01:47:32,737 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992157-0c24
2025-07-17 01:47:32,737 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992155-1a22
2025-07-17 01:47:32,737 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992163-09ae
2025-07-17 01:47:32,738 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992155-7123
2025-07-17 01:47:32,738 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992157-bb96
2025-07-17 01:47:32,756 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992156-cc31
2025-07-17 01:47:32,757 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:32,757 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992159-6214
2025-07-17 01:47:32,758 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:32,758 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992151-f392
2025-07-17 01:47:32,759 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:32,760 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992162-1016
2025-07-17 01:47:32,760 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:32,760 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:32,760 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:32,761 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:32,762 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:32,764 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:32,764 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992153-d5c4
2025-07-17 01:47:32,765 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992151-d104
2025-07-17 01:47:32,765 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992153-fea7
2025-07-17 01:47:32,765 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992151-60c3
2025-07-17 01:47:32,765 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992164-c01c
2025-07-17 01:47:32,765 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992159-406e
2025-07-17 01:47:32,788 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992164-3669
2025-07-17 01:47:32,840 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992163-9f88
2025-07-17 01:47:32,841 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992164-fe2f
2025-07-17 01:47:32,843 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992163-2539
2025-07-17 01:47:32,843 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:32,843 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992162-c4c4
2025-07-17 01:47:32,844 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992164-dd2c
2025-07-17 01:47:32,844 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992165-d0f1
2025-07-17 01:47:32,844 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 166.0900
2025-07-17 01:47:32,844 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 166.1100
2025-07-17 01:47:32,844 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 166.1300
2025-07-17 01:47:32,845 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 166.1500
2025-07-17 01:47:32,845 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 166.1900
2025-07-17 01:47:32,846 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 166.2400
2025-07-17 01:47:32,846 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 166.5500
2025-07-17 01:47:32,847 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 166.7200
2025-07-17 01:47:32,847 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 166.9400
2025-07-17 01:47:32,847 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 167.2200
2025-07-17 01:47:32,847 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 167.5600
2025-07-17 01:47:32,848 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 167.9500
2025-07-17 01:47:32,848 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 168.8000
2025-07-17 01:47:32,848 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 169.2300
2025-07-17 01:47:32,848 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 169.6100
2025-07-17 01:47:32,849 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 169.9500
2025-07-17 01:47:32,849 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 170.2300
2025-07-17 01:47:32,849 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 170.4500
2025-07-17 01:47:32,849 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 170.6300
2025-07-17 01:47:32,850 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 170.7600
2025-07-17 01:47:32,850 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 170.8600
2025-07-17 01:47:32,850 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 170.9300
2025-07-17 01:47:32,850 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 170.9800
2025-07-17 01:47:32,850 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 171.0200
2025-07-17 01:47:32,851 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 171.0500
2025-07-17 01:47:32,851 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 171.0700
2025-07-17 01:47:32,851 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 171.0800
2025-07-17 01:47:33,074 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,078 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,079 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,079 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,079 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,079 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,080 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,080 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,082 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,087 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,401 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,401 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,401 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,402 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,402 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,403 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,403 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,404 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,404 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,482 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,482 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992160-de46
2025-07-17 01:47:33,483 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992152-964e
2025-07-17 01:47:33,483 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992164-672d
2025-07-17 01:47:33,483 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992154-a7e7
2025-07-17 01:47:33,484 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992155-40b4
2025-07-17 01:47:33,484 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992157-0c24
2025-07-17 01:47:33,484 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992155-7123
2025-07-17 01:47:33,484 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992163-09ae
2025-07-17 01:47:33,484 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992155-1a22
2025-07-17 01:47:33,486 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992157-bb96
2025-07-17 01:47:33,486 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992156-cc31
2025-07-17 01:47:33,487 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992159-6214
2025-07-17 01:47:33,487 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992151-f392
2025-07-17 01:47:33,487 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992151-d104
2025-07-17 01:47:33,487 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992153-fea7
2025-07-17 01:47:33,487 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992162-1016
2025-07-17 01:47:33,487 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992153-d5c4
2025-07-17 01:47:33,487 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992151-60c3
2025-07-17 01:47:33,487 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992164-c01c
2025-07-17 01:47:33,487 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992159-406e
2025-07-17 01:47:33,487 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992164-3669
2025-07-17 01:47:33,488 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992163-9f88
2025-07-17 01:47:33,488 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992164-fe2f
2025-07-17 01:47:33,488 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992162-c4c4
2025-07-17 01:47:33,488 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992164-dd2c
2025-07-17 01:47:33,488 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992165-d0f1
2025-07-17 01:47:33,488 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992163-2539
2025-07-17 01:48:10,720 - INFO - [close] - 正在关闭程序...
2025-07-17 01:48:10,837 - INFO - [cancel_all_open_orders] - 正在取消 30 个挂单...
2025-07-17 01:48:10,899 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-17 01:48:10,899 - INFO - [close] - 程序已关闭。
2025-07-17 01:50:17,461 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 01:50:17,461 - INFO - [_initialize_exchange] - 已设置代理: http://127.0.0.1:7897
2025-07-17 01:50:17,468 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 01:50:17,468 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 01:50:17,470 - INFO - [setup] - 正在执行启动设置...
2025-07-17 01:50:17,470 - INFO - [test_connection] - 正在测试API连接...
2025-07-17 01:50:17,470 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 01:50:38,832 - INFO - [close] - 正在关闭程序...
2025-07-17 01:50:39,144 - ERROR - [full_state_sync] - 状态完全同步失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752688238895&recvWindow=5000&signature=d8ad93292eddc9efbe6491351e38eb15e2caec51886d1b532f0b417afc74e2c4
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1283, in _wrap_create_connection
    return await self._loop.create_connection(*args, **kwargs, sock=sock)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\asyncio\base_events.py", line 1148, in create_connection
    transport, protocol = await self._create_connection_transport(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\asyncio\base_events.py", line 1181, in _create_connection_transport
    await waiter
  File "C:\Python312\Lib\asyncio\selector_events.py", line 988, in _read_ready__get_buffer
    nbytes = self._sock.recv_into(buf)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 1488, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 642, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1209, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1581, in _create_direct_connection
    raise last_exc
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1550, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1291, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host api.binance.com:443 ssl:default [远程主机强迫关闭了一个现有的连接。]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 550, in full_state_sync
    positions, open_orders, ticker = await asyncio.gather(positions_f, orders_f, ticker_f)
                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 4104, in fetch_ticker
    await self.load_markets()
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 327, in load_markets
    raise e
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.ExchangeNotAvailable: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752688238895&recvWindow=5000&signature=d8ad93292eddc9efbe6491351e38eb15e2caec51886d1b532f0b417afc74e2c4
2025-07-17 01:50:39,397 - INFO - [close] - 程序已关闭。
2025-07-17 01:50:49,136 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 01:50:49,136 - INFO - [_initialize_exchange] - 已设置代理: http://127.0.0.1:7897
2025-07-17 01:50:49,144 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 01:50:49,144 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 01:50:49,146 - INFO - [setup] - 正在执行启动设置...
2025-07-17 01:50:49,146 - INFO - [test_connection] - 正在测试API连接...
2025-07-17 01:50:49,146 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 01:51:10,505 - ERROR - [test_connection] - 连接测试失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 01:51:10,505 - ERROR - [setup] - 初始化设置失败: API连接测试失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 226, in setup
    if not await self.test_connection(): raise Exception("API连接测试失败")
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: API连接测试失败
2025-07-17 01:51:10,506 - CRITICAL - [main] - 程序顶层发生未捕获的严重错误: API连接测试失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 662, in main
    await bot.run()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 254, in run
    await self.setup()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 226, in setup
    if not await self.test_connection(): raise Exception("API连接测试失败")
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: API连接测试失败
2025-07-17 01:51:10,507 - INFO - [close] - 正在关闭程序...
2025-07-17 01:51:31,601 - ERROR - [full_state_sync] - 状态完全同步失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752688270568&recvWindow=5000&signature=262acecbcce907fa2c8447755793908945a13538567ed4bc8f815ad86c49b1f1
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1268, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohappyeyeballs\impl.py", line 149, in start_connection
    raise OSError(msg)
OSError: Multiple exceptions: [Errno 10060] Connect call failed ('************', 443), [Errno 10051] Connect call failed ('2001::b93c:d832', 443, 0, 0)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 1488, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 642, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1209, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1581, in _create_direct_connection
    raise last_exc
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1550, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1291, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host api.binance.com:443 ssl:default [None]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 550, in full_state_sync
    positions, open_orders, ticker = await asyncio.gather(positions_f, orders_f, ticker_f)
                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 4104, in fetch_ticker
    await self.load_markets()
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 327, in load_markets
    raise e
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.ExchangeNotAvailable: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752688270568&recvWindow=5000&signature=262acecbcce907fa2c8447755793908945a13538567ed4bc8f815ad86c49b1f1
2025-07-17 01:51:31,864 - INFO - [close] - 程序已关闭。
2025-07-17 01:51:31,864 - INFO - [main] - 程序已完全退出。
2025-07-17 01:52:25,909 - INFO - [main] - 启动前预检 API 连接...
2025-07-17 01:52:25,916 - INFO - [main] - 正在测试 ping 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897)
2025-07-17 01:52:26,000 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.08s
2025-07-17 01:52:26,001 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 01:52:26,001 - INFO - [_initialize_exchange] - 已设置代理: http://127.0.0.1:7897
2025-07-17 01:52:26,008 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 01:52:26,008 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 01:52:26,010 - INFO - [setup] - 正在执行启动设置...
2025-07-17 01:52:26,010 - INFO - [test_connection] - 正在测试API连接...
2025-07-17 01:52:26,010 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 01:52:47,368 - ERROR - [test_connection] - 连接测试失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 01:52:47,368 - ERROR - [setup] - 初始化设置失败: API连接测试失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 226, in setup
    if not await self.test_connection(): raise Exception("API连接测试失败")
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: API连接测试失败
2025-07-17 01:52:47,370 - CRITICAL - [main] - 程序顶层发生未捕获的严重错误: API连接测试失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 687, in main
    await bot.run()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 254, in run
    await self.setup()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 226, in setup
    if not await self.test_connection(): raise Exception("API连接测试失败")
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: API连接测试失败
2025-07-17 01:52:47,370 - INFO - [close] - 正在关闭程序...
2025-07-17 01:53:08,500 - ERROR - [full_state_sync] - 状态完全同步失败: binanceusdm GET https://api.binance.com/sapi/v1/margin/allPairs?timestamp=1752688367476&recvWindow=5000&signature=ca99a2e554df1f26398d4a5b19e14201722dc2f8da58c3219e617833eab79077
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1268, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohappyeyeballs\impl.py", line 149, in start_connection
    raise OSError(msg)
OSError: Multiple exceptions: [Errno 10051] Connect call failed ('2001::9df0:1124', 443, 0, 0), [Errno 10060] Connect call failed ('***************', 443)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 1488, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 642, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1209, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1581, in _create_direct_connection
    raise last_exc
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1550, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1291, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host api.binance.com:443 ssl:default [None]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 550, in full_state_sync
    positions, open_orders, ticker = await asyncio.gather(positions_f, orders_f, ticker_f)
                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 4104, in fetch_ticker
    await self.load_markets()
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 327, in load_markets
    raise e
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.ExchangeNotAvailable: binanceusdm GET https://api.binance.com/sapi/v1/margin/allPairs?timestamp=1752688367476&recvWindow=5000&signature=ca99a2e554df1f26398d4a5b19e14201722dc2f8da58c3219e617833eab79077
2025-07-17 01:53:08,761 - INFO - [close] - 程序已关闭。
2025-07-17 01:53:08,761 - INFO - [main] - 程序已完全退出。
2025-07-17 01:54:48,716 - INFO - [main] - 启动前预检 API 连接...
2025-07-17 01:54:48,723 - INFO - [main] - 正在测试 ping 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897)
2025-07-17 01:54:48,803 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.08s
2025-07-17 01:54:48,804 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 01:54:48,804 - INFO - [_initialize_exchange] - 已设置代理: http://127.0.0.1:7897
2025-07-17 01:54:48,812 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 01:54:48,812 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 01:54:48,813 - INFO - [setup] - 正在执行启动设置...
2025-07-17 01:54:48,814 - INFO - [test_connection] - 正在测试API连接...
2025-07-17 01:54:48,814 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 01:54:49,381 - ERROR - [test_connection] - 连接测试失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 01:54:49,381 - ERROR - [setup] - 初始化设置失败: API连接测试失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 226, in setup
    if not await self.test_connection(): raise Exception("API连接测试失败")
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: API连接测试失败
2025-07-17 01:54:49,382 - CRITICAL - [main] - 程序顶层发生未捕获的严重错误: API连接测试失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 687, in main
    await bot.run()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 254, in run
    await self.setup()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 226, in setup
    if not await self.test_connection(): raise Exception("API连接测试失败")
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: API连接测试失败
2025-07-17 01:54:49,382 - INFO - [close] - 正在关闭程序...
2025-07-17 01:55:24,162 - INFO - [main] - 启动前预检 API 连接...
2025-07-17 01:55:24,168 - INFO - [main] - 正在测试 ping 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897)
2025-07-17 01:55:24,249 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.08s
2025-07-17 01:55:24,250 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 01:55:24,250 - INFO - [_initialize_exchange] - 已设置代理: http://127.0.0.1:7897
2025-07-17 01:55:24,258 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 01:55:24,259 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 01:55:24,261 - INFO - [setup] - 正在执行启动设置...
2025-07-17 01:55:24,261 - INFO - [test_connection] - 正在测试API连接...
2025-07-17 01:55:24,261 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 01:55:45,614 - ERROR - [test_connection] - 连接测试失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 01:55:45,614 - ERROR - [setup] - 初始化设置失败: API连接测试失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 226, in setup
    if not await self.test_connection(): raise Exception("API连接测试失败")
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: API连接测试失败
2025-07-17 01:55:45,615 - CRITICAL - [main] - 程序顶层发生未捕获的严重错误: API连接测试失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 687, in main
    await bot.run()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 254, in run
    await self.setup()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 226, in setup
    if not await self.test_connection(): raise Exception("API连接测试失败")
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: API连接测试失败
2025-07-17 01:55:45,615 - INFO - [close] - 正在关闭程序...
2025-07-17 01:56:06,753 - WARNING - [__del__] - binanceusdm requires to release all resources with an explicit call to the .close() coroutine. If you are using the exchange instance with async coroutines, add `await exchange.close()` to your code into a place when you're done with the exchange and don't need the exchange instance anymore (at the end of your async coroutine).
2025-07-17 01:56:06,754 - ERROR - [default_exception_handler] - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000023EFFF93C50>
2025-07-17 01:58:33,918 - INFO - [main] - 启动前预检 API 连接...
2025-07-17 01:58:33,918 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-17 01:58:36,374 - INFO - [main] - 代理服务器连接正常
2025-07-17 01:58:36,379 - INFO - [main] - 正在测试 ping 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/3
2025-07-17 01:58:36,471 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.09s
2025-07-17 01:58:36,472 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 01:58:36,472 - INFO - [_initialize_exchange] - 已设置代理: http://127.0.0.1:7897
2025-07-17 01:58:36,479 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 01:58:36,479 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 01:58:36,481 - INFO - [setup] - 正在执行启动设置...
2025-07-17 01:58:36,481 - INFO - [test_connection] - 正在测试API连接... (尝试 1/3)
2025-07-17 01:58:36,481 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 01:58:57,832 - ERROR - [test_connection] - 连接测试失败 (尝试 1/3): binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 01:58:57,832 - ERROR - [setup] - 初始化设置失败: API连接测试失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 257, in setup
    if not await self.test_connection(): raise Exception("API连接测试失败")
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: API连接测试失败
2025-07-17 01:58:57,833 - CRITICAL - [main] - 程序顶层发生未捕获的严重错误: API连接测试失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 768, in main
    await bot.run()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 285, in run
    await self.setup()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 257, in setup
    if not await self.test_connection(): raise Exception("API连接测试失败")
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: API连接测试失败
2025-07-17 01:58:57,834 - INFO - [close] - 正在关闭程序...
2025-07-17 01:58:58,098 - INFO - [close] - 程序已关闭。
2025-07-17 01:58:58,098 - INFO - [main] - 程序已完全退出。
2025-07-17 02:00:38,730 - INFO - [main] - 启动前预检 API 连接...
2025-07-17 02:00:38,730 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-17 02:00:41,389 - INFO - [main] - 代理服务器连接正常
2025-07-17 02:00:41,394 - INFO - [main] - 正在测试 ping 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/3
2025-07-17 02:00:41,474 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.08s
2025-07-17 02:00:41,475 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 02:00:41,476 - INFO - [_initialize_exchange] - 已设置代理: http://127.0.0.1:7897
2025-07-17 02:00:41,483 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 02:00:41,484 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 02:00:41,485 - INFO - [setup] - 正在执行启动设置...
2025-07-17 02:00:41,485 - INFO - [test_connection] - 正在测试API连接... (尝试 1/3)
2025-07-17 02:00:41,485 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 02:01:03,107 - ERROR - [test_connection] - Ping失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 02:01:03,107 - INFO - [test_connection] - 尝试使用基础HTTP请求测试连接...
2025-07-17 02:01:03,288 - INFO - [test_connection] - 基础HTTP ping成功
2025-07-17 02:01:03,288 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 02:01:24,418 - ERROR - [test_connection] - Ticker获取失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752688863353&recvWindow=10000&signature=3b2f638250a678d42da4ad0b1eaa9481a33ec9d6a1e212f352c2845a1b1f172b
2025-07-17 02:01:24,418 - INFO - [test_connection] - 尝试获取服务器时间...
2025-07-17 02:01:45,462 - ERROR - [test_connection] - 网络连接失败 (尝试 1/3): binanceusdm GET https://fapi.binance.com/fapi/v1/time
2025-07-17 02:01:45,462 - ERROR - [test_connection] - 请检查代理服务器是否正常运行
2025-07-17 02:01:45,463 - INFO - [test_connection] - 等待 5 秒后重试...
2025-07-17 02:01:50,474 - INFO - [test_connection] - 正在测试API连接... (尝试 2/3)
2025-07-17 02:01:50,474 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 02:02:11,565 - ERROR - [test_connection] - Ping失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 02:02:11,565 - INFO - [test_connection] - 尝试使用基础HTTP请求测试连接...
2025-07-17 02:02:11,651 - INFO - [test_connection] - 基础HTTP ping成功
2025-07-17 02:02:11,652 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 02:02:32,917 - ERROR - [test_connection] - Ticker获取失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752688931706&recvWindow=10000&signature=9f5303561104c9b91b7b06a093063e1bb3c1a0ca3f91f887663f74acfe1d4062
2025-07-17 02:02:32,917 - INFO - [test_connection] - 尝试获取服务器时间...
2025-07-17 02:02:53,976 - ERROR - [test_connection] - 网络连接失败 (尝试 2/3): binanceusdm GET https://fapi.binance.com/fapi/v1/time
2025-07-17 02:02:53,977 - ERROR - [test_connection] - 请检查代理服务器是否正常运行
2025-07-17 02:02:53,977 - INFO - [test_connection] - 等待 5 秒后重试...
2025-07-17 02:02:58,988 - INFO - [test_connection] - 正在测试API连接... (尝试 3/3)
2025-07-17 02:02:58,988 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 02:03:20,070 - ERROR - [test_connection] - Ping失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 02:03:20,070 - INFO - [test_connection] - 尝试使用基础HTTP请求测试连接...
2025-07-17 02:03:20,157 - INFO - [test_connection] - 基础HTTP ping成功
2025-07-17 02:03:20,157 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 02:03:41,262 - ERROR - [test_connection] - Ticker获取失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689000210&recvWindow=10000&signature=284b96cb4d97f64bde13e83dfb92a81cbbb16d48b1762b257831ecb1855281dd
2025-07-17 02:03:41,262 - INFO - [test_connection] - 尝试获取服务器时间...
2025-07-17 02:04:02,308 - ERROR - [test_connection] - 网络连接失败 (尝试 3/3): binanceusdm GET https://fapi.binance.com/fapi/v1/time
2025-07-17 02:04:02,308 - ERROR - [test_connection] - 请检查代理服务器是否正常运行
2025-07-17 02:04:02,308 - ERROR - [test_connection] - 所有连接测试尝试均失败
2025-07-17 02:04:02,308 - ERROR - [setup] - 初始化设置失败: API连接测试失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 279, in setup
    if not await self.test_connection(): raise Exception("API连接测试失败")
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: API连接测试失败
2025-07-17 02:04:02,309 - CRITICAL - [main] - 程序顶层发生未捕获的严重错误: API连接测试失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 790, in main
    await bot.run()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 307, in run
    await self.setup()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 279, in setup
    if not await self.test_connection(): raise Exception("API连接测试失败")
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: API连接测试失败
2025-07-17 02:04:02,310 - INFO - [close] - 正在关闭程序...
2025-07-17 02:04:02,573 - INFO - [close] - 程序已关闭。
2025-07-17 02:04:02,573 - INFO - [main] - 程序已完全退出。
2025-07-17 02:04:29,675 - INFO - [main] - 启动前预检 API 连接...
2025-07-17 02:04:29,676 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-17 02:04:30,212 - INFO - [main] - 代理服务器连接正常
2025-07-17 02:04:30,217 - INFO - [main] - 正在测试 ping 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/3
2025-07-17 02:04:30,300 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.08s
2025-07-17 02:04:30,301 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 02:04:30,301 - WARNING - [_initialize_exchange] - 检测到代理配置: http://127.0.0.1:7897，但由于CCXT库兼容性问题，暂时禁用代理
2025-07-17 02:04:30,301 - WARNING - [_initialize_exchange] - 如果您在中国大陆，请确保您的网络环境可以直接访问Binance API
2025-07-17 02:04:30,309 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 02:04:30,309 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 02:04:30,310 - INFO - [setup] - 正在执行启动设置...
2025-07-17 02:04:30,311 - INFO - [test_connection] - 正在测试API连接... (尝试 1/3)
2025-07-17 02:04:30,311 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 02:04:51,666 - ERROR - [test_connection] - Ping失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 02:04:51,666 - INFO - [test_connection] - 尝试使用基础HTTP请求测试连接...
2025-07-17 02:04:51,751 - INFO - [test_connection] - 基础HTTP ping成功
2025-07-17 02:04:51,752 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 02:04:52,071 - ERROR - [test_connection] - Ticker获取失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689091806&recvWindow=10000&signature=d6bd83213dff8d98f638e6e453acc31ccfe8bfeee47e293f4ee80b717f82188c
2025-07-17 02:04:52,071 - INFO - [test_connection] - 尝试获取服务器时间...
2025-07-17 02:05:13,128 - INFO - [close] - 正在关闭程序...
2025-07-17 02:05:13,391 - INFO - [close] - 程序已关闭。
2025-07-17 02:06:55,555 - INFO - [main] - 启动前预检 API 连接...
2025-07-17 02:06:55,555 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-17 02:06:57,013 - INFO - [main] - 代理服务器连接正常
2025-07-17 02:06:57,018 - INFO - [main] - 正在测试 ping 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/3
2025-07-17 02:06:57,096 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.08s
2025-07-17 02:06:57,096 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 02:06:57,096 - INFO - [_initialize_exchange] - 检测到代理配置: http://127.0.0.1:7897
2025-07-17 02:06:57,096 - INFO - [_initialize_exchange] - 已设置代理环境变量
2025-07-17 02:06:57,103 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 02:06:57,103 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 02:06:57,105 - INFO - [setup] - 正在执行启动设置...
2025-07-17 02:06:57,106 - INFO - [test_connection] - 正在测试API连接... (尝试 1/3)
2025-07-17 02:06:57,106 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 02:07:18,477 - ERROR - [test_connection] - Ping失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 02:07:18,477 - INFO - [test_connection] - 尝试使用基础HTTP请求测试连接...
2025-07-17 02:07:18,559 - INFO - [test_connection] - 基础HTTP ping成功
2025-07-17 02:07:18,560 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 02:07:18,789 - ERROR - [test_connection] - Ticker获取失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689238614&recvWindow=10000&signature=167022a0976cd974e608a9e1632c3145eba49a3e6ef0ac65d82f76274a0029d1
2025-07-17 02:07:18,790 - INFO - [test_connection] - 尝试获取服务器时间...
2025-07-17 02:07:39,854 - ERROR - [test_connection] - 网络连接失败 (尝试 1/3): binanceusdm GET https://fapi.binance.com/fapi/v1/time
2025-07-17 02:07:39,854 - ERROR - [test_connection] - 请检查代理服务器是否正常运行
2025-07-17 02:07:39,854 - INFO - [test_connection] - 等待 5 秒后重试...
2025-07-17 02:07:44,865 - INFO - [test_connection] - 正在测试API连接... (尝试 2/3)
2025-07-17 02:07:44,866 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 02:08:05,950 - ERROR - [test_connection] - Ping失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 02:08:05,950 - INFO - [test_connection] - 尝试使用基础HTTP请求测试连接...
2025-07-17 02:08:06,033 - INFO - [test_connection] - 基础HTTP ping成功
2025-07-17 02:08:06,034 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 02:08:06,258 - ERROR - [test_connection] - Ticker获取失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689286091&recvWindow=10000&signature=99c5747eba6071004c552de0130c957699c3d06425de7c51c1142736e67c05c4
2025-07-17 02:08:06,258 - INFO - [test_connection] - 尝试获取服务器时间...
2025-07-17 02:08:27,304 - ERROR - [test_connection] - 网络连接失败 (尝试 2/3): binanceusdm GET https://fapi.binance.com/fapi/v1/time
2025-07-17 02:08:27,304 - ERROR - [test_connection] - 请检查代理服务器是否正常运行
2025-07-17 02:08:27,305 - INFO - [test_connection] - 等待 5 秒后重试...
2025-07-17 02:08:32,311 - INFO - [test_connection] - 正在测试API连接... (尝试 3/3)
2025-07-17 02:08:32,311 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 02:08:53,383 - ERROR - [test_connection] - Ping失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 02:08:53,383 - INFO - [test_connection] - 尝试使用基础HTTP请求测试连接...
2025-07-17 02:08:53,464 - INFO - [test_connection] - 基础HTTP ping成功
2025-07-17 02:08:53,465 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 02:09:14,544 - ERROR - [test_connection] - Ticker获取失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689333522&recvWindow=10000&signature=ab83eed03ecc7f8df3c3c56b046d12878da5159671d957a491d11fb0249137a3
2025-07-17 02:09:14,544 - INFO - [test_connection] - 尝试获取服务器时间...
2025-07-17 02:09:35,635 - ERROR - [test_connection] - 网络连接失败 (尝试 3/3): binanceusdm GET https://fapi.binance.com/fapi/v1/time
2025-07-17 02:09:35,635 - ERROR - [test_connection] - 请检查代理服务器是否正常运行
2025-07-17 02:09:35,635 - ERROR - [test_connection] - 所有连接测试尝试均失败
2025-07-17 02:09:35,636 - ERROR - [setup] - 初始化设置失败: API连接测试失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 292, in setup
    if not await self.test_connection(): raise Exception("API连接测试失败")
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: API连接测试失败
2025-07-17 02:09:35,636 - CRITICAL - [main] - 程序顶层发生未捕获的严重错误: API连接测试失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 803, in main
    await bot.run()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 320, in run
    await self.setup()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 292, in setup
    if not await self.test_connection(): raise Exception("API连接测试失败")
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: API连接测试失败
2025-07-17 02:09:35,637 - INFO - [close] - 正在关闭程序...
2025-07-17 02:09:35,900 - INFO - [close] - 程序已关闭。
2025-07-17 02:09:35,900 - INFO - [main] - 程序已完全退出。
2025-07-17 02:10:16,379 - INFO - [main] - 启动前预检 API 连接...
2025-07-17 02:10:16,379 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-17 02:10:17,847 - INFO - [main] - 代理服务器连接正常
2025-07-17 02:10:17,852 - INFO - [main] - 正在测试 ping 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/3
2025-07-17 02:10:17,937 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.09s
2025-07-17 02:10:17,937 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 02:10:17,937 - INFO - [_initialize_exchange] - 检测到代理配置: http://127.0.0.1:7897
2025-07-17 02:10:17,938 - INFO - [_initialize_exchange] - 已设置代理环境变量
2025-07-17 02:10:17,946 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 02:10:17,946 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 02:10:17,948 - INFO - [setup] - 正在执行启动设置...
2025-07-17 02:10:17,948 - INFO - [test_connection] - 正在测试API连接... (尝试 1/3)
2025-07-17 02:10:17,948 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 02:10:39,281 - ERROR - [test_connection] - Ping失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 02:10:39,281 - INFO - [test_connection] - 尝试使用基础HTTP请求测试连接...
2025-07-17 02:10:39,378 - INFO - [test_connection] - 基础HTTP ping成功
2025-07-17 02:10:39,379 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 02:11:00,492 - ERROR - [test_connection] - Ticker获取失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689439437&recvWindow=10000&signature=bc8cf1993ab69661b7e8e8091ab21a91e7c46e6cef1db13265c5bf6f04074560
2025-07-17 02:11:00,492 - INFO - [test_connection] - 尝试获取服务器时间...
2025-07-17 02:11:21,565 - ERROR - [test_connection] - 网络连接失败 (尝试 1/3): binanceusdm GET https://fapi.binance.com/fapi/v1/time
2025-07-17 02:11:21,565 - ERROR - [test_connection] - 请检查代理服务器是否正常运行
2025-07-17 02:11:21,565 - INFO - [test_connection] - 等待 5 秒后重试...
2025-07-17 02:11:26,569 - INFO - [test_connection] - 正在测试API连接... (尝试 2/3)
2025-07-17 02:11:26,569 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 02:11:47,640 - ERROR - [test_connection] - Ping失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 02:11:47,640 - INFO - [test_connection] - 尝试使用基础HTTP请求测试连接...
2025-07-17 02:11:47,720 - INFO - [test_connection] - 基础HTTP ping成功
2025-07-17 02:11:47,720 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 02:11:47,931 - ERROR - [test_connection] - Ticker获取失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689507762&recvWindow=10000&signature=d58daac9525341ff3f379091e4858fb3f37df1a93025ed8f81e5c77c223db390
2025-07-17 02:11:47,932 - INFO - [test_connection] - 尝试获取服务器时间...
2025-07-17 02:12:08,962 - ERROR - [test_connection] - 网络连接失败 (尝试 2/3): binanceusdm GET https://fapi.binance.com/fapi/v1/time
2025-07-17 02:12:08,962 - ERROR - [test_connection] - 请检查代理服务器是否正常运行
2025-07-17 02:12:08,963 - INFO - [test_connection] - 等待 5 秒后重试...
2025-07-17 02:12:13,971 - INFO - [test_connection] - 正在测试API连接... (尝试 3/3)
2025-07-17 02:12:13,971 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 02:12:35,056 - ERROR - [test_connection] - Ping失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 02:12:35,056 - INFO - [test_connection] - 尝试使用基础HTTP请求测试连接...
2025-07-17 02:12:35,138 - INFO - [test_connection] - 基础HTTP ping成功
2025-07-17 02:12:35,139 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 02:12:35,365 - ERROR - [test_connection] - Ticker获取失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689555195&recvWindow=10000&signature=614dc4bc1e6160e73161ca14a16099cdaffa136b9aef5de1984dc90b0fe2d422
2025-07-17 02:12:35,365 - INFO - [test_connection] - 尝试获取服务器时间...
2025-07-17 02:12:56,398 - ERROR - [test_connection] - 网络连接失败 (尝试 3/3): binanceusdm GET https://fapi.binance.com/fapi/v1/time
2025-07-17 02:12:56,399 - ERROR - [test_connection] - 请检查代理服务器是否正常运行
2025-07-17 02:12:56,399 - ERROR - [test_connection] - 所有连接测试尝试均失败
2025-07-17 02:12:56,399 - WARNING - [setup] - API连接测试失败，但继续尝试初始化...
2025-07-17 02:12:56,399 - INFO - [setup] - 正在加载市场数据...
2025-07-17 02:12:56,624 - ERROR - [setup] - 初始化设置失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689576445&recvWindow=10000&signature=92ca2854923229eb288e94be0a333bf078d706b6de2c18192c713f3c58fb18c9
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1283, in _wrap_create_connection
    return await self._loop.create_connection(*args, **kwargs, sock=sock)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\asyncio\base_events.py", line 1148, in create_connection
    transport, protocol = await self._create_connection_transport(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\asyncio\base_events.py", line 1181, in _create_connection_transport
    await waiter
  File "C:\Python312\Lib\asyncio\selector_events.py", line 988, in _read_ready__get_buffer
    nbytes = self._sock.recv_into(buf)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 1488, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 642, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1209, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1581, in _create_direct_connection
    raise last_exc
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1550, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1291, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host api.binance.com:443 ssl:default [远程主机强迫关闭了一个现有的连接。]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 301, in setup
    await self.exchange.load_markets(True)
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 327, in load_markets
    raise e
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 284, in load_markets_helper
    currencies = await self.fetch_currencies()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 2813, in fetch_currencies
    results = await asyncio.gather(*promises)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 11339, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 932, in fetch2
    raise e
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 166, in fetch
    return await super().fetch(url, method, headers, body)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 246, in fetch
    raise ExchangeNotAvailable(details) from e
ccxt.base.errors.ExchangeNotAvailable: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689576445&recvWindow=10000&signature=92ca2854923229eb288e94be0a333bf078d706b6de2c18192c713f3c58fb18c9
2025-07-17 02:12:56,630 - CRITICAL - [main] - 程序顶层发生未捕获的严重错误: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689576445&recvWindow=10000&signature=92ca2854923229eb288e94be0a333bf078d706b6de2c18192c713f3c58fb18c9
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1283, in _wrap_create_connection
    return await self._loop.create_connection(*args, **kwargs, sock=sock)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\asyncio\base_events.py", line 1148, in create_connection
    transport, protocol = await self._create_connection_transport(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\asyncio\base_events.py", line 1181, in _create_connection_transport
    await waiter
  File "C:\Python312\Lib\asyncio\selector_events.py", line 988, in _read_ready__get_buffer
    nbytes = self._sock.recv_into(buf)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 1488, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 642, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1209, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1581, in _create_direct_connection
    raise last_exc
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1550, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1291, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host api.binance.com:443 ssl:default [远程主机强迫关闭了一个现有的连接。]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 809, in main
    await bot.run()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 326, in run
    await self.setup()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 301, in setup
    await self.exchange.load_markets(True)
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 327, in load_markets
    raise e
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 284, in load_markets_helper
    currencies = await self.fetch_currencies()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 2813, in fetch_currencies
    results = await asyncio.gather(*promises)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 11339, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 932, in fetch2
    raise e
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 166, in fetch
    return await super().fetch(url, method, headers, body)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 246, in fetch
    raise ExchangeNotAvailable(details) from e
ccxt.base.errors.ExchangeNotAvailable: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689576445&recvWindow=10000&signature=92ca2854923229eb288e94be0a333bf078d706b6de2c18192c713f3c58fb18c9
2025-07-17 02:12:56,632 - INFO - [close] - 正在关闭程序...
2025-07-17 02:12:56,883 - INFO - [close] - 程序已关闭。
2025-07-17 02:12:56,883 - INFO - [main] - 程序已完全退出。
2025-07-17 02:13:27,626 - INFO - [main] - 启动前预检 API 连接...
2025-07-17 02:13:27,627 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-17 02:13:29,267 - INFO - [main] - 代理服务器连接正常
2025-07-17 02:13:29,271 - INFO - [main] - 正在测试 ping 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/3
2025-07-17 02:13:29,351 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.08s
2025-07-17 02:13:29,352 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 02:13:29,352 - INFO - [_initialize_exchange] - 检测到代理配置: http://127.0.0.1:7897
2025-07-17 02:13:29,352 - INFO - [_initialize_exchange] - 已设置代理环境变量
2025-07-17 02:13:29,359 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 02:13:29,359 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 02:13:29,360 - INFO - [setup] - 正在执行启动设置...
2025-07-17 02:13:29,360 - INFO - [test_connection] - 正在测试API连接... (尝试 1/3)
2025-07-17 02:13:29,360 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 02:13:50,701 - ERROR - [test_connection] - Ping失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 02:13:50,701 - INFO - [test_connection] - 尝试使用基础HTTP请求测试连接...
2025-07-17 02:13:50,786 - INFO - [test_connection] - 基础HTTP ping成功
2025-07-17 02:13:50,787 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 02:14:11,882 - ERROR - [test_connection] - Ticker获取失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689630841&recvWindow=10000&signature=13ec3af029a6b4fc18dea2f0b5e1bf1e48f68738b612208dfef92a5050992fa8
2025-07-17 02:14:11,882 - INFO - [test_connection] - 尝试获取服务器时间...
2025-07-17 02:14:32,923 - ERROR - [test_connection] - 网络连接失败 (尝试 1/3): binanceusdm GET https://fapi.binance.com/fapi/v1/time
2025-07-17 02:14:32,923 - ERROR - [test_connection] - 请检查代理服务器是否正常运行
2025-07-17 02:14:32,923 - INFO - [test_connection] - 等待 5 秒后重试...
2025-07-17 02:14:37,938 - INFO - [test_connection] - 正在测试API连接... (尝试 2/3)
2025-07-17 02:14:37,938 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 02:14:59,047 - ERROR - [test_connection] - Ping失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 02:14:59,047 - INFO - [test_connection] - 尝试使用基础HTTP请求测试连接...
2025-07-17 02:14:59,134 - INFO - [test_connection] - 基础HTTP ping成功
2025-07-17 02:14:59,135 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 02:15:20,228 - ERROR - [test_connection] - Ticker获取失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689699189&recvWindow=10000&signature=79ccc055cb3eb9b1cd0f0395b3e7bdc29d809beae22823dd8de3680dd14cee81
2025-07-17 02:15:20,228 - INFO - [test_connection] - 尝试获取服务器时间...
2025-07-17 02:15:41,273 - ERROR - [test_connection] - 网络连接失败 (尝试 2/3): binanceusdm GET https://fapi.binance.com/fapi/v1/time
2025-07-17 02:15:41,273 - ERROR - [test_connection] - 请检查代理服务器是否正常运行
2025-07-17 02:15:41,273 - INFO - [test_connection] - 等待 5 秒后重试...
2025-07-17 02:15:46,287 - INFO - [test_connection] - 正在测试API连接... (尝试 3/3)
2025-07-17 02:15:46,287 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 02:16:07,397 - ERROR - [test_connection] - Ping失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 02:16:07,397 - INFO - [test_connection] - 尝试使用基础HTTP请求测试连接...
2025-07-17 02:16:07,481 - INFO - [test_connection] - 基础HTTP ping成功
2025-07-17 02:16:07,482 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 02:16:28,564 - ERROR - [test_connection] - Ticker获取失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689767520&recvWindow=10000&signature=2c0b228c4ca7971ac034510a444cd35db626c27f988b66849a51d5b250c8aae2
2025-07-17 02:16:28,564 - INFO - [test_connection] - 尝试获取服务器时间...
2025-07-17 02:16:49,591 - ERROR - [test_connection] - 网络连接失败 (尝试 3/3): binanceusdm GET https://fapi.binance.com/fapi/v1/time
2025-07-17 02:16:49,591 - ERROR - [test_connection] - 请检查代理服务器是否正常运行
2025-07-17 02:16:49,591 - ERROR - [test_connection] - 所有连接测试尝试均失败
2025-07-17 02:16:49,592 - WARNING - [setup] - API连接测试失败，但继续尝试初始化...
2025-07-17 02:16:49,592 - INFO - [setup] - 正在加载市场数据...
2025-07-17 02:17:10,665 - WARNING - [setup] - 加载市场数据失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689809638&recvWindow=10000&signature=a850d761e3a5a625f69a2da508f6e2c5be076a541916ab8a5253da2a7b1dacc1，使用默认市场配置
2025-07-17 02:17:31,742 - ERROR - [set_hedge_mode] - 设置双向持仓模式失败: binanceusdm POST https://fapi.binance.com/fapi/v1/positionSide/dual
2025-07-17 02:17:52,830 - ERROR - [set_leverage] - 设置杠杆失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689851789&recvWindow=10000&signature=a548cd343c91686f100b5a3cb44eb05f34b86e199a0dd9b79ba81e6ffa0c97c2
2025-07-17 02:18:13,891 - ERROR - [full_state_sync] - 状态完全同步失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689872846&recvWindow=10000&signature=a718122a8ffc282879bff2cf549ef9a6bd7661a96ed047a0d6f7417e6eff2da2
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1268, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohappyeyeballs\impl.py", line 149, in start_connection
    raise OSError(msg)
OSError: Multiple exceptions: [Errno 10060] Connect call failed ('************', 443), [Errno 10051] Connect call failed ('2001::6ca0:a7a7', 443, 0, 0)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 1488, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 642, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1209, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1581, in _create_direct_connection
    raise last_exc
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1550, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1291, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host api.binance.com:443 ssl:default [None]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 676, in full_state_sync
    positions, open_orders, ticker = await asyncio.gather(positions_f, orders_f, ticker_f)
                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 4104, in fetch_ticker
    await self.load_markets()
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 327, in load_markets
    raise e
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.ExchangeNotAvailable: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689872846&recvWindow=10000&signature=a718122a8ffc282879bff2cf549ef9a6bd7661a96ed047a0d6f7417e6eff2da2
2025-07-17 02:18:34,937 - WARNING - [setup] - 获取价格失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689893896&recvWindow=10000&signature=c29d39f9898702932fdf3747675f6c6cd8831dbd18faa9e62005bc24d1bcaadc，使用默认价格
2025-07-17 02:18:55,976 - ERROR - [update_price_series] - 更新K线数据失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689914937&recvWindow=10000&signature=8dffe5c33be146973250e0d96c841e703b537ad9be0c7ddd1ce1fc4538ef2a5e
2025-07-17 02:18:55,976 - INFO - [fetch_listen_key] - 正在获取新的Listen Key...
2025-07-17 02:19:17,074 - ERROR - [fetch_listen_key] - 获取 listen key 失败: binanceusdm POST https://fapi.binance.com/fapi/v1/listenKey
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1268, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohappyeyeballs\impl.py", line 149, in start_connection
    raise OSError(msg)
OSError: Multiple exceptions: [Errno 10060] Connect call failed ('*************', 443), [Errno 10051] Connect call failed ('2001::c710:9c0b', 443, 0, 0)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 1488, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 642, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1209, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1581, in _create_direct_connection
    raise last_exc
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1550, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1291, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host fapi.binance.com:443 ssl:default [None]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 757, in fetch_listen_key
    response = await self.exchange.fapiPrivatePostListenKey()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 11339, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 932, in fetch2
    raise e
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 166, in fetch
    return await super().fetch(url, method, headers, body)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 246, in fetch
    raise ExchangeNotAvailable(details) from e
ccxt.base.errors.ExchangeNotAvailable: binanceusdm POST https://fapi.binance.com/fapi/v1/listenKey
2025-07-17 02:19:17,076 - ERROR - [setup] - 初始化设置失败: binanceusdm POST https://fapi.binance.com/fapi/v1/listenKey
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1268, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohappyeyeballs\impl.py", line 149, in start_connection
    raise OSError(msg)
OSError: Multiple exceptions: [Errno 10060] Connect call failed ('*************', 443), [Errno 10051] Connect call failed ('2001::c710:9c0b', 443, 0, 0)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 1488, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 642, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1209, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1581, in _create_direct_connection
    raise last_exc
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1550, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1291, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host fapi.binance.com:443 ssl:default [None]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 346, in setup
    self.listen_key = await self.fetch_listen_key()
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 757, in fetch_listen_key
    response = await self.exchange.fapiPrivatePostListenKey()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 11339, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 932, in fetch2
    raise e
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 166, in fetch
    return await super().fetch(url, method, headers, body)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 246, in fetch
    raise ExchangeNotAvailable(details) from e
ccxt.base.errors.ExchangeNotAvailable: binanceusdm POST https://fapi.binance.com/fapi/v1/listenKey
2025-07-17 02:19:17,077 - CRITICAL - [main] - 程序顶层发生未捕获的严重错误: binanceusdm POST https://fapi.binance.com/fapi/v1/listenKey
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1268, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohappyeyeballs\impl.py", line 149, in start_connection
    raise OSError(msg)
OSError: Multiple exceptions: [Errno 10060] Connect call failed ('*************', 443), [Errno 10051] Connect call failed ('2001::c710:9c0b', 443, 0, 0)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 1488, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 642, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1209, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1581, in _create_direct_connection
    raise last_exc
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1550, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1291, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host fapi.binance.com:443 ssl:default [None]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 841, in main
    await bot.run()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 358, in run
    await self.setup()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 346, in setup
    self.listen_key = await self.fetch_listen_key()
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 757, in fetch_listen_key
    response = await self.exchange.fapiPrivatePostListenKey()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 11339, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 932, in fetch2
    raise e
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 166, in fetch
    return await super().fetch(url, method, headers, body)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 246, in fetch
    raise ExchangeNotAvailable(details) from e
ccxt.base.errors.ExchangeNotAvailable: binanceusdm POST https://fapi.binance.com/fapi/v1/listenKey
2025-07-17 02:19:17,079 - INFO - [close] - 正在关闭程序...
2025-07-17 02:19:27,094 - WARNING - [close] - 关闭时取消挂单失败，但程序将继续关闭: 
2025-07-17 02:19:27,357 - INFO - [close] - 程序已关闭。
2025-07-17 02:19:27,357 - INFO - [main] - 程序已完全退出。
2025-07-17 22:32:15,827 - INFO - [main] - 启动前预检 API 连接...
2025-07-17 22:32:15,827 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-17 22:32:26,312 - ERROR - [main] - 代理服务器连接失败: 
2025-07-17 22:32:26,313 - ERROR - [main] - 请检查代理服务器是否正在运行且配置正确
2025-07-17 22:32:43,385 - INFO - [main] - 启动前预检 API 连接...
2025-07-17 22:32:43,385 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-17 22:32:44,862 - INFO - [main] - 代理服务器连接正常
2025-07-17 22:32:44,867 - INFO - [main] - 正在测试 ping 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/3
2025-07-17 22:32:44,951 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.08s
2025-07-17 22:32:44,951 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 22:32:44,952 - INFO - [_initialize_exchange] - 已设置代理: http://127.0.0.1:7897
2025-07-17 22:32:44,959 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 22:32:44,959 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 22:32:44,961 - INFO - [setup] - 正在执行启动设置...
2025-07-17 22:32:44,961 - INFO - [test_connection] - 正在测试API连接... (尝试 1/3)
2025-07-17 22:32:44,961 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 22:33:00,491 - ERROR - [test_connection] - Ping失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 22:33:00,491 - INFO - [test_connection] - 尝试使用基础HTTP请求测试连接...
2025-07-17 22:33:00,572 - INFO - [test_connection] - 基础HTTP ping成功
2025-07-17 22:33:00,572 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 22:33:21,699 - ERROR - [test_connection] - Ticker获取失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752762780632&recvWindow=10000&signature=cc7b9f71c7aa4c6ffd12d52caced6a1835171ebb425aca7d5ff8c3857bb024a4
2025-07-17 22:33:21,699 - INFO - [test_connection] - 尝试获取服务器时间...
2025-07-17 22:33:21,883 - ERROR - [test_connection] - 网络连接失败 (尝试 1/3): binanceusdm GET https://fapi.binance.com/fapi/v1/time
2025-07-17 22:33:21,883 - ERROR - [test_connection] - 请检查代理服务器是否正常运行
2025-07-17 22:33:21,884 - INFO - [test_connection] - 等待 5 秒后重试...
2025-07-17 22:33:26,897 - INFO - [test_connection] - 正在测试API连接... (尝试 2/3)
2025-07-17 22:33:26,897 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 22:33:28,144 - ERROR - [test_connection] - Ping失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 22:33:28,144 - INFO - [test_connection] - 尝试使用基础HTTP请求测试连接...
2025-07-17 22:33:28,231 - INFO - [test_connection] - 基础HTTP ping成功
2025-07-17 22:33:28,232 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 22:33:49,302 - ERROR - [test_connection] - Ticker获取失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752762808271&recvWindow=10000&signature=f40e154ee4ceaa0e4c5c3df6e77a69e058447c6c75f37223deffc3c8e730daef
2025-07-17 22:33:49,302 - INFO - [test_connection] - 尝试获取服务器时间...
2025-07-17 22:33:49,485 - ERROR - [test_connection] - 网络连接失败 (尝试 2/3): binanceusdm GET https://fapi.binance.com/fapi/v1/time
2025-07-17 22:33:49,485 - ERROR - [test_connection] - 请检查代理服务器是否正常运行
2025-07-17 22:33:49,485 - INFO - [test_connection] - 等待 5 秒后重试...
2025-07-17 22:33:54,491 - INFO - [close] - 正在关闭程序...
2025-07-17 22:33:54,752 - INFO - [close] - 程序已关闭。
2025-07-17 22:40:37,239 - INFO - [main] - 启动前预检 API 连接...
2025-07-17 22:40:37,240 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-17 22:40:37,752 - INFO - [main] - 代理服务器连接正常 (测试URL: http://httpbin.org/ip)
2025-07-17 22:40:37,758 - INFO - [main] - 正在测试 ping 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/3
2025-07-17 22:40:37,869 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.11s
2025-07-17 22:40:37,870 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 22:40:37,870 - INFO - [_initialize_exchange] - 已设置代理: http://127.0.0.1:7897
2025-07-17 22:40:37,879 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 22:40:37,879 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 22:40:37,880 - INFO - [setup] - 正在执行启动设置...
2025-07-17 22:40:37,881 - INFO - [test_connection] - 正在测试API连接... (尝试 1/3)
2025-07-17 22:40:37,881 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 22:40:59,249 - ERROR - [test_connection] - Ping失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 22:40:59,249 - ERROR - [test_connection] - 错误类型: ExchangeNotAvailable
2025-07-17 22:40:59,249 - INFO - [test_connection] - 尝试使用基础HTTP请求测试连接...
2025-07-17 22:40:59,331 - INFO - [test_connection] - 基础HTTP ping成功
2025-07-17 22:40:59,331 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 22:41:20,435 - ERROR - [test_connection] - Ticker获取失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752763259388&recvWindow=10000&signature=c05d9723e0a9630ab48a607f17e1433b4b115059f1b8b1d82dfc3cbc9cdf64cd
2025-07-17 22:41:20,435 - INFO - [test_connection] - 尝试获取服务器时间...
2025-07-17 22:41:41,488 - ERROR - [test_connection] - 网络连接失败 (尝试 1/3): binanceusdm GET https://fapi.binance.com/fapi/v1/time
2025-07-17 22:41:41,488 - ERROR - [test_connection] - 请检查代理服务器是否正常运行
2025-07-17 22:41:41,488 - INFO - [test_connection] - 等待 5 秒后重试...
2025-07-17 22:41:46,494 - INFO - [test_connection] - 正在测试API连接... (尝试 2/3)
2025-07-17 22:41:46,494 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 22:42:07,572 - ERROR - [test_connection] - Ping失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 22:42:07,572 - ERROR - [test_connection] - 错误类型: ExchangeNotAvailable
2025-07-17 22:42:07,573 - INFO - [test_connection] - 尝试使用基础HTTP请求测试连接...
2025-07-17 22:42:07,656 - INFO - [test_connection] - 基础HTTP ping成功
2025-07-17 22:42:07,657 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 22:42:28,755 - INFO - [close] - 正在关闭程序...
2025-07-17 22:42:29,019 - INFO - [close] - 程序已关闭。
2025-07-17 22:43:38,964 - INFO - [main] - 启动前预检 API 连接...
2025-07-17 22:43:38,964 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-17 22:43:40,686 - INFO - [main] - 代理服务器连接正常 (测试URL: http://httpbin.org/ip)
2025-07-17 22:43:40,691 - INFO - [main] - 正在测试 ping 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/3
2025-07-17 22:43:40,877 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.19s
2025-07-17 22:43:40,878 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 22:43:40,878 - INFO - [_initialize_exchange] - 已设置代理: http://127.0.0.1:7897
2025-07-17 22:43:40,884 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 22:43:40,885 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 22:43:40,886 - INFO - [setup] - 正在执行启动设置...
2025-07-17 22:43:40,887 - INFO - [test_connection] - 正在测试API连接... (尝试 1/3)
2025-07-17 22:43:40,887 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 22:44:02,291 - ERROR - [test_connection] - Ping失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 22:44:02,291 - ERROR - [test_connection] - 错误类型: ExchangeNotAvailable
2025-07-17 22:44:02,291 - INFO - [test_connection] - 尝试使用基础HTTP请求测试连接...
2025-07-17 22:44:02,375 - INFO - [test_connection] - 基础HTTP ping成功
2025-07-17 22:44:02,375 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 22:44:23,619 - ERROR - [test_connection] - Ticker获取失败: binanceusdm GET https://api.binance.com/sapi/v1/margin/allPairs?timestamp=1752763442476&recvWindow=10000&signature=50a314b6b23d2bebc579b803639b1d914d6181f8ed10ccb01f1d9575a0890aa1
2025-07-17 22:44:23,619 - INFO - [test_connection] - 尝试获取服务器时间...
2025-07-17 22:44:44,673 - ERROR - [test_connection] - 网络连接失败 (尝试 1/3): binanceusdm GET https://fapi.binance.com/fapi/v1/time
2025-07-17 22:44:44,673 - ERROR - [test_connection] - 请检查代理服务器是否正常运行
2025-07-17 22:44:44,674 - INFO - [test_connection] - 等待 5 秒后重试...
2025-07-17 22:44:49,677 - INFO - [close] - 正在关闭程序...
2025-07-17 22:44:49,941 - INFO - [close] - 程序已关闭。
2025-07-17 22:56:26,340 - INFO - [main] - 启动前预检 API 连接...
2025-07-17 22:56:26,341 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-17 22:56:28,182 - INFO - [main] - 代理服务器连接正常 (测试URL: http://httpbin.org/ip)
2025-07-17 22:56:28,193 - INFO - [main] - 正在测试 ping 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/3
2025-07-17 22:56:28,274 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.08s
2025-07-17 22:56:28,275 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 22:56:28,275 - INFO - [_initialize_exchange] - 已设置代理: http://127.0.0.1:7897
2025-07-17 22:56:28,283 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 22:56:28,283 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 22:56:28,285 - INFO - [setup] - 正在执行启动设置...
2025-07-17 22:56:28,285 - INFO - [test_connection] - 正在测试API连接... (尝试 1/3)
2025-07-17 22:56:28,285 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 22:56:49,625 - ERROR - [test_connection] - Ping失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 22:56:49,625 - ERROR - [test_connection] - 错误类型: ExchangeNotAvailable
2025-07-17 22:56:49,625 - INFO - [test_connection] - 尝试使用基础HTTP请求测试连接...
2025-07-17 22:56:49,729 - INFO - [test_connection] - 基础HTTP ping成功
2025-07-17 22:56:49,729 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-17 22:57:10,850 - ERROR - [test_connection] - 服务器时间获取失败: binanceusdm GET https://fapi.binance.com/fapi/v1/time
2025-07-17 22:57:10,850 - INFO - [test_connection] - 正在加载市场数据...
2025-07-17 22:57:11,076 - ERROR - [test_connection] - 市场数据加载失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752764230896&recvWindow=10000&signature=82df014880a3ce9d637149038717f5b33002a580e8651fa216785e6ceb1537e7
2025-07-17 22:57:11,077 - WARNING - [test_connection] - 市场数据加载失败，但继续测试...
2025-07-17 22:57:11,077 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 22:57:11,249 - ERROR - [test_connection] - Ticker获取失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752764231077&recvWindow=10000&signature=d0e8fc514a0aaa8eb9743b028c69a786ffc8250f63dbd0d6e38b74c90460c0f0
2025-07-17 22:57:11,249 - WARNING - [test_connection] - Ticker获取失败，但这可能是交易对问题，继续测试...
2025-07-17 22:57:11,250 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-17 22:57:11,444 - ERROR - [test_connection] - 余额获取失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752764231267&recvWindow=10000&signature=f3c05923d123c228b5a28603771e00615d8ea38ef8f8e232a8357186291af5c2
2025-07-17 22:57:11,444 - WARNING - [test_connection] - 余额获取失败，但这可能是API权限问题，继续运行...
2025-07-17 22:57:11,444 - INFO - [test_connection] - API连接测试完成！
2025-07-17 22:57:11,444 - INFO - [setup] - 正在加载市场数据...
2025-07-17 22:57:11,667 - ERROR - [setup] - 初始化设置失败: binanceusdm GET https://api.binance.com/sapi/v1/margin/allPairs?timestamp=1752764231498&recvWindow=10000&signature=382598b42ed94be0dd21a6cb5a8c89eb689ba3b5e36a4e750701f2c2583555d8
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1283, in _wrap_create_connection
    return await self._loop.create_connection(*args, **kwargs, sock=sock)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\asyncio\base_events.py", line 1148, in create_connection
    transport, protocol = await self._create_connection_transport(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\asyncio\base_events.py", line 1181, in _create_connection_transport
    await waiter
  File "C:\Python312\Lib\asyncio\selector_events.py", line 988, in _read_ready__get_buffer
    nbytes = self._sock.recv_into(buf)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 1488, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 642, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1209, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1581, in _create_direct_connection
    raise last_exc
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1550, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1291, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host api.binance.com:443 ssl:default [远程主机强迫关闭了一个现有的连接。]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 321, in setup
    await self.exchange.load_markets(True)
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 327, in load_markets
    raise e
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 284, in load_markets_helper
    currencies = await self.fetch_currencies()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 2813, in fetch_currencies
    results = await asyncio.gather(*promises)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 11339, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 932, in fetch2
    raise e
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 166, in fetch
    return await super().fetch(url, method, headers, body)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 246, in fetch
    raise ExchangeNotAvailable(details) from e
ccxt.base.errors.ExchangeNotAvailable: binanceusdm GET https://api.binance.com/sapi/v1/margin/allPairs?timestamp=1752764231498&recvWindow=10000&signature=382598b42ed94be0dd21a6cb5a8c89eb689ba3b5e36a4e750701f2c2583555d8
2025-07-17 22:57:11,673 - CRITICAL - [main] - 程序顶层发生未捕获的严重错误: binanceusdm GET https://api.binance.com/sapi/v1/margin/allPairs?timestamp=1752764231498&recvWindow=10000&signature=382598b42ed94be0dd21a6cb5a8c89eb689ba3b5e36a4e750701f2c2583555d8
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1283, in _wrap_create_connection
    return await self._loop.create_connection(*args, **kwargs, sock=sock)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\asyncio\base_events.py", line 1148, in create_connection
    transport, protocol = await self._create_connection_transport(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\asyncio\base_events.py", line 1181, in _create_connection_transport
    await waiter
  File "C:\Python312\Lib\asyncio\selector_events.py", line 988, in _read_ready__get_buffer
    nbytes = self._sock.recv_into(buf)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 1488, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 642, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1209, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1581, in _create_direct_connection
    raise last_exc
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1550, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1291, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host api.binance.com:443 ssl:default [远程主机强迫关闭了一个现有的连接。]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 853, in main
    await bot.run()
  File "C:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 347, in run
    await self.setup()
  File "C:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 321, in setup
    await self.exchange.load_markets(True)
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 327, in load_markets
    raise e
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 284, in load_markets_helper
    currencies = await self.fetch_currencies()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 2813, in fetch_currencies
    results = await asyncio.gather(*promises)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 11339, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 932, in fetch2
    raise e
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 166, in fetch
    return await super().fetch(url, method, headers, body)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 246, in fetch
    raise ExchangeNotAvailable(details) from e
ccxt.base.errors.ExchangeNotAvailable: binanceusdm GET https://api.binance.com/sapi/v1/margin/allPairs?timestamp=1752764231498&recvWindow=10000&signature=382598b42ed94be0dd21a6cb5a8c89eb689ba3b5e36a4e750701f2c2583555d8
2025-07-17 22:57:11,675 - INFO - [close] - 正在关闭程序...
2025-07-17 22:57:11,932 - INFO - [close] - 程序已关闭。
2025-07-17 22:57:11,932 - INFO - [main] - 程序已完全退出。
2025-07-17 22:57:50,551 - INFO - [main] - 启动前预检 API 连接...
2025-07-17 22:57:50,552 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-17 22:57:52,377 - INFO - [main] - 代理服务器连接正常 (测试URL: http://httpbin.org/ip)
2025-07-17 22:57:52,382 - INFO - [main] - 正在测试 ping 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/3
2025-07-17 22:57:52,470 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.09s
2025-07-17 22:57:52,471 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 22:57:52,471 - INFO - [_initialize_exchange] - 已设置代理: http://127.0.0.1:7897
2025-07-17 22:57:52,477 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 22:57:52,478 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 22:57:52,479 - INFO - [setup] - 正在执行启动设置...
2025-07-17 22:57:52,479 - INFO - [test_connection] - 正在测试API连接... (尝试 1/3)
2025-07-17 22:57:52,479 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 22:58:13,826 - ERROR - [test_connection] - Ping失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 22:58:13,826 - ERROR - [test_connection] - 错误类型: ExchangeNotAvailable
2025-07-17 22:58:13,826 - INFO - [test_connection] - 尝试使用基础HTTP请求测试连接...
2025-07-17 22:58:13,910 - INFO - [test_connection] - 基础HTTP ping成功
2025-07-17 22:58:13,911 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-17 22:58:34,980 - ERROR - [test_connection] - 服务器时间获取失败: binanceusdm GET https://fapi.binance.com/fapi/v1/time
2025-07-17 22:58:34,980 - INFO - [test_connection] - 正在加载市场数据...
2025-07-17 22:58:56,077 - ERROR - [test_connection] - 市场数据加载失败: binanceusdm GET https://fapi.binance.com/fapi/v1/exchangeInfo
2025-07-17 22:58:56,077 - WARNING - [test_connection] - 市场数据加载失败，但继续测试...
2025-07-17 22:58:56,077 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 22:59:17,171 - ERROR - [test_connection] - Ticker获取失败: binanceusdm GET https://fapi.binance.com/fapi/v1/exchangeInfo
2025-07-17 22:59:17,172 - WARNING - [test_connection] - Ticker获取失败，但这可能是交易对问题，继续测试...
2025-07-17 22:59:17,172 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-17 22:59:38,277 - ERROR - [test_connection] - 余额获取失败: binanceusdm GET https://fapi.binance.com/fapi/v1/exchangeInfo
2025-07-17 22:59:38,277 - WARNING - [test_connection] - 余额获取失败，但这可能是API权限问题，继续运行...
2025-07-17 22:59:38,278 - INFO - [test_connection] - API连接测试完成！
2025-07-17 22:59:38,278 - INFO - [setup] - 正在加载市场数据...
2025-07-17 22:59:59,345 - ERROR - [setup] - 初始化设置失败: binanceusdm GET https://fapi.binance.com/fapi/v1/exchangeInfo
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1268, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohappyeyeballs\impl.py", line 149, in start_connection
    raise OSError(msg)
OSError: Multiple exceptions: [Errno 10060] Connect call failed ('*************', 443), [Errno 10051] Connect call failed ('2001::1f0d:5615', 443, 0, 0)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 1488, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 642, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1209, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1581, in _create_direct_connection
    raise last_exc
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1550, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1291, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host fapi.binance.com:443 ssl:default [None]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 327, in setup
    await self.exchange.load_markets(True)
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 327, in load_markets
    raise e
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 286, in load_markets_helper
    markets = await self.fetch_markets(params)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 3054, in fetch_markets
    results = await asyncio.gather(*promisesRaw)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 11339, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 932, in fetch2
    raise e
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 166, in fetch
    return await super().fetch(url, method, headers, body)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 246, in fetch
    raise ExchangeNotAvailable(details) from e
ccxt.base.errors.ExchangeNotAvailable: binanceusdm GET https://fapi.binance.com/fapi/v1/exchangeInfo
2025-07-17 22:59:59,351 - CRITICAL - [main] - 程序顶层发生未捕获的严重错误: binanceusdm GET https://fapi.binance.com/fapi/v1/exchangeInfo
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1268, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohappyeyeballs\impl.py", line 149, in start_connection
    raise OSError(msg)
OSError: Multiple exceptions: [Errno 10060] Connect call failed ('*************', 443), [Errno 10051] Connect call failed ('2001::1f0d:5615', 443, 0, 0)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 1488, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 642, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1209, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1581, in _create_direct_connection
    raise last_exc
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1550, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1291, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host fapi.binance.com:443 ssl:default [None]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 859, in main
    await bot.run()
  File "C:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 353, in run
    await self.setup()
  File "C:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 327, in setup
    await self.exchange.load_markets(True)
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 327, in load_markets
    raise e
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 286, in load_markets_helper
    markets = await self.fetch_markets(params)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 3054, in fetch_markets
    results = await asyncio.gather(*promisesRaw)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 11339, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 932, in fetch2
    raise e
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 166, in fetch
    return await super().fetch(url, method, headers, body)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 246, in fetch
    raise ExchangeNotAvailable(details) from e
ccxt.base.errors.ExchangeNotAvailable: binanceusdm GET https://fapi.binance.com/fapi/v1/exchangeInfo
2025-07-17 22:59:59,353 - INFO - [close] - 正在关闭程序...
2025-07-17 22:59:59,608 - INFO - [close] - 程序已关闭。
2025-07-17 22:59:59,608 - INFO - [main] - 程序已完全退出。
2025-07-17 23:01:33,481 - INFO - [main] - 启动前预检 API 连接...
2025-07-17 23:01:33,481 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-17 23:01:37,134 - INFO - [main] - 代理服务器连接正常 (测试URL: http://httpbin.org/ip)
2025-07-17 23:01:37,139 - INFO - [main] - 正在测试 ping 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/3
2025-07-17 23:01:37,234 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.10s
2025-07-17 23:01:37,235 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 23:01:37,235 - INFO - [_initialize_exchange] - 已设置代理: http://127.0.0.1:7897
2025-07-17 23:01:37,242 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 23:01:37,242 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 23:01:37,243 - INFO - [setup] - 正在执行启动设置...
2025-07-17 23:01:37,243 - INFO - [test_connection] - 正在测试API连接... (尝试 1/3)
2025-07-17 23:01:37,243 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 23:01:58,600 - ERROR - [test_connection] - Ping失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 23:01:58,600 - ERROR - [test_connection] - 错误类型: ExchangeNotAvailable
2025-07-17 23:01:58,600 - INFO - [test_connection] - 尝试使用基础HTTP请求测试连接...
2025-07-17 23:01:58,683 - INFO - [test_connection] - 基础HTTP ping成功
2025-07-17 23:01:58,684 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-17 23:02:19,771 - ERROR - [test_connection] - 服务器时间获取失败: binanceusdm GET https://fapi.binance.com/fapi/v1/time
2025-07-17 23:02:19,771 - INFO - [test_connection] - 正在加载市场数据...
2025-07-17 23:02:40,873 - ERROR - [test_connection] - 市场数据加载失败: binanceusdm GET https://fapi.binance.com/fapi/v1/exchangeInfo
2025-07-17 23:02:40,874 - WARNING - [test_connection] - 市场数据加载失败，但继续测试...
2025-07-17 23:02:40,874 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 23:03:02,007 - ERROR - [test_connection] - Ticker获取失败: binanceusdm GET https://fapi.binance.com/fapi/v1/exchangeInfo
2025-07-17 23:03:02,007 - WARNING - [test_connection] - Ticker获取失败，但这可能是交易对问题，继续测试...
2025-07-17 23:03:02,007 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-17 23:03:23,090 - ERROR - [test_connection] - 余额获取失败: binanceusdm GET https://fapi.binance.com/fapi/v1/exchangeInfo
2025-07-17 23:03:23,090 - WARNING - [test_connection] - 余额获取失败，但这可能是API权限问题，继续运行...
2025-07-17 23:03:23,090 - INFO - [test_connection] - API连接测试完成！
2025-07-17 23:03:23,090 - INFO - [setup] - 正在加载市场数据...
2025-07-17 23:03:44,160 - INFO - [close] - 正在关闭程序...
2025-07-17 23:03:44,410 - INFO - [close] - 程序已关闭。
2025-07-17 23:34:03,657 - INFO - [main] - 启动前预检 API 连接...
2025-07-17 23:34:03,658 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-17 23:34:03,665 - INFO - [main] - 测试代理连接: http://httpbin.org/ip
2025-07-17 23:34:18,672 - WARNING - [main] - 代理测试URL http://httpbin.org/ip 失败: Timeout on reading data from socket
2025-07-17 23:34:18,672 - INFO - [main] - 测试代理连接: https://api.binance.com/api/v3/ping
2025-07-17 23:34:18,759 - INFO - [main] - 代理服务器连接正常 (测试URL: https://api.binance.com/api/v3/ping)
2025-07-17 23:34:18,760 - INFO - [main] - 代理服务器测试通过
2025-07-17 23:34:18,765 - INFO - [main] - 正在测试 API 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/5
2025-07-17 23:34:18,844 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.08s
2025-07-17 23:34:18,844 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 23:34:18,844 - INFO - [_initialize_exchange] - 已设置代理: http://127.0.0.1:7897
2025-07-17 23:34:18,852 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 23:34:18,852 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 23:34:18,853 - INFO - [setup] - 正在执行启动设置...
2025-07-17 23:34:18,853 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-17 23:34:18,853 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-17 23:34:18,858 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-17 23:34:18,949 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-17 23:34:18,950 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-17 23:34:19,339 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-17 23:34:19,339 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-17 23:34:19,670 - INFO - [test_connection] - 服务器时间获取成功: 1752766459193
2025-07-17 23:34:19,671 - INFO - [test_connection] - 正在加载市场数据...
2025-07-17 23:34:22,268 - INFO - [test_connection] - 市场数据加载成功
2025-07-17 23:34:22,268 - INFO - [test_connection] - 交易对 SOL/USDT:USDT 验证成功
2025-07-17 23:34:22,268 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 23:34:23,528 - INFO - [test_connection] - Ticker 获取成功: SOL/USDT:USDT @ 173.73
2025-07-17 23:34:23,528 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-17 23:34:24,797 - INFO - [test_connection] - 余额获取成功，USDT余额: 1656.93173557
2025-07-17 23:34:24,797 - INFO - [test_connection] - API连接测试完成！
2025-07-17 23:34:24,798 - INFO - [setup] - 正在加载市场数据...
2025-07-17 23:34:27,393 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-17 23:34:28,660 - INFO - [set_hedge_mode] - 当前已是双向持仓模式，无需更改
2025-07-17 23:34:29,933 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-17 23:34:34,804 - WARNING - [full_state_sync] - 挂单数据格式异常
2025-07-17 23:34:38,540 - INFO - [fetch_listen_key] - 成功获取新的 listen key: QtHdX16DhU...
2025-07-17 23:34:38,541 - INFO - [setup] - 初始化设置完成。
2025-07-17 23:34:38,541 - INFO - [setup] - 当前持仓: 多头=0.0, 空头=0.0
2025-07-17 23:34:42,207 - WARNING - [full_state_sync] - 挂单数据格式异常
2025-07-17 23:34:42,207 - INFO - [cancel_all_open_orders] - 正在尝试取消所有挂单...
2025-07-17 23:34:43,477 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-17 23:34:43,478 - INFO - [run] - 所有现有挂单已取消，准备启动策略...
2025-07-17 23:34:43,478 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/QtHdX16DhUqt5aA7jIhGigXLdeKcwDw9IErkpbdQiOQ6uNQyVJgGJUHcLUV5LFQ6
2025-07-17 23:34:43,492 - ERROR - [run] - WebSocket连接失败 (1/10): BaseEventLoop.create_connection() got an unexpected keyword argument 'extra_headers'
2025-07-17 23:34:43,492 - INFO - [run] - 等待 10 秒后重连...
2025-07-17 23:34:54,750 - INFO - [fetch_listen_key] - 成功获取新的 listen key: QtHdX16DhU...
2025-07-17 23:34:54,750 - INFO - [run] - 已获取新的ListenKey
2025-07-17 23:34:54,750 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/QtHdX16DhUqt5aA7jIhGigXLdeKcwDw9IErkpbdQiOQ6uNQyVJgGJUHcLUV5LFQ6
2025-07-17 23:34:54,750 - ERROR - [run] - WebSocket连接失败 (2/10): BaseEventLoop.create_connection() got an unexpected keyword argument 'extra_headers'
2025-07-17 23:34:54,750 - INFO - [run] - 等待 20 秒后重连...
2025-07-17 23:35:16,044 - INFO - [fetch_listen_key] - 成功获取新的 listen key: QtHdX16DhU...
2025-07-17 23:35:16,045 - INFO - [run] - 已获取新的ListenKey
2025-07-17 23:35:16,045 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/QtHdX16DhUqt5aA7jIhGigXLdeKcwDw9IErkpbdQiOQ6uNQyVJgGJUHcLUV5LFQ6
2025-07-17 23:35:16,045 - ERROR - [run] - WebSocket连接失败 (3/10): BaseEventLoop.create_connection() got an unexpected keyword argument 'extra_headers'
2025-07-17 23:35:16,045 - INFO - [run] - 等待 40 秒后重连...
2025-07-17 23:35:57,354 - INFO - [fetch_listen_key] - 成功获取新的 listen key: QtHdX16DhU...
2025-07-17 23:35:57,354 - INFO - [run] - 已获取新的ListenKey
2025-07-17 23:35:57,354 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/QtHdX16DhUqt5aA7jIhGigXLdeKcwDw9IErkpbdQiOQ6uNQyVJgGJUHcLUV5LFQ6
2025-07-17 23:35:57,354 - ERROR - [run] - WebSocket连接失败 (4/10): BaseEventLoop.create_connection() got an unexpected keyword argument 'extra_headers'
2025-07-17 23:35:57,355 - INFO - [run] - 等待 60 秒后重连...
2025-07-17 23:36:58,635 - INFO - [fetch_listen_key] - 成功获取新的 listen key: QtHdX16DhU...
2025-07-17 23:36:58,635 - INFO - [run] - 已获取新的ListenKey
2025-07-17 23:36:58,636 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/QtHdX16DhUqt5aA7jIhGigXLdeKcwDw9IErkpbdQiOQ6uNQyVJgGJUHcLUV5LFQ6
2025-07-17 23:36:58,636 - ERROR - [run] - WebSocket连接失败 (5/10): BaseEventLoop.create_connection() got an unexpected keyword argument 'extra_headers'
2025-07-17 23:36:58,636 - INFO - [run] - 等待 60 秒后重连...
2025-07-17 23:37:58,628 - INFO - [close] - 正在关闭程序...
2025-07-17 23:37:58,629 - INFO - [cancel_all_open_orders] - 正在尝试取消所有挂单...
2025-07-17 23:37:59,924 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-17 23:38:00,185 - INFO - [close] - 程序已关闭。
2025-07-17 23:39:45,740 - INFO - [main] - 启动前预检 API 连接...
2025-07-17 23:39:45,740 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-17 23:39:45,747 - INFO - [main] - 测试代理连接: http://httpbin.org/ip
2025-07-17 23:39:46,952 - INFO - [main] - 代理服务器连接正常 (测试URL: http://httpbin.org/ip)
2025-07-17 23:39:46,952 - INFO - [main] - 代理服务器测试通过
2025-07-17 23:39:46,957 - INFO - [main] - 正在测试 API 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/5
2025-07-17 23:39:47,039 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.08s
2025-07-17 23:39:47,040 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 23:39:47,040 - INFO - [_initialize_exchange] - 已设置代理: http://127.0.0.1:7897
2025-07-17 23:39:47,047 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 23:39:47,048 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 23:39:47,049 - INFO - [setup] - 正在执行启动设置...
2025-07-17 23:39:47,049 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-17 23:39:47,050 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-17 23:39:47,054 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-17 23:39:47,141 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-17 23:39:47,142 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-17 23:39:47,535 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-17 23:39:47,535 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-17 23:39:47,857 - INFO - [test_connection] - 服务器时间获取成功: 1752766787380
2025-07-17 23:39:47,858 - INFO - [test_connection] - 正在加载市场数据...
2025-07-17 23:39:50,447 - INFO - [test_connection] - 市场数据加载成功
2025-07-17 23:39:50,447 - INFO - [test_connection] - 交易对 SOL/USDT:USDT 验证成功
2025-07-17 23:39:50,447 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 23:39:51,714 - INFO - [test_connection] - Ticker 获取成功: SOL/USDT:USDT @ 174.25
2025-07-17 23:39:51,714 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-17 23:39:52,968 - INFO - [test_connection] - 余额获取成功，USDT余额: 1656.93173557
2025-07-17 23:39:52,968 - INFO - [test_connection] - API连接测试完成！
2025-07-17 23:39:52,968 - INFO - [setup] - 正在加载市场数据...
2025-07-17 23:39:55,548 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-17 23:39:56,824 - INFO - [set_hedge_mode] - 当前已是双向持仓模式，无需更改
2025-07-17 23:39:58,097 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-17 23:40:02,947 - WARNING - [full_state_sync] - 挂单数据格式异常
2025-07-17 23:40:06,690 - INFO - [fetch_listen_key] - 成功获取新的 listen key: QtHdX16DhU...
2025-07-17 23:40:06,690 - INFO - [setup] - 初始化设置完成。
2025-07-17 23:40:06,690 - INFO - [setup] - 当前持仓: 多头=0.0, 空头=0.0
2025-07-17 23:40:10,342 - WARNING - [full_state_sync] - 挂单数据格式异常
2025-07-17 23:40:10,342 - INFO - [cancel_all_open_orders] - 正在尝试取消所有挂单...
2025-07-17 23:40:11,611 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-17 23:40:11,611 - INFO - [run] - 所有现有挂单已取消，准备启动策略...
2025-07-17 23:40:11,611 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/QtHdX16DhUqt5aA7jIhGigXLdeKcwDw9IErkpbdQiOQ6uNQyVJgGJUHcLUV5LFQ6
2025-07-17 23:40:11,807 - INFO - [run] - WebSocket 连接成功。
2025-07-17 23:40:11,807 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdt@bookTicker'], 'id': 1}
2025-07-17 23:40:15,461 - WARNING - [full_state_sync] - 挂单数据格式异常
2025-07-17 23:40:15,463 - INFO - [execute_argm_strategy] - 网格重绘触发！新中枢: 174.6717, 新范围: [172.9096, 176.4338]
2025-07-17 23:40:15,463 - INFO - [rebalance_grid_orders] - 准备创建 16 个新订单
2025-07-17 23:40:15,464 - INFO - [place_order] - 准备下单: sell SHORT 0.1100 @ 174.5200 (尝试 1/3)
2025-07-17 23:40:15,464 - INFO - [place_order] - 准备下单: sell SHORT 0.1100 @ 174.8200 (尝试 1/3)
2025-07-17 23:40:15,464 - INFO - [place_order] - 准备下单: sell SHORT 0.1100 @ 175.1200 (尝试 1/3)
2025-07-17 23:40:15,465 - INFO - [place_order] - 准备下单: sell SHORT 0.1100 @ 175.3900 (尝试 1/3)
2025-07-17 23:40:15,465 - INFO - [place_order] - 准备下单: sell SHORT 0.1100 @ 175.6200 (尝试 1/3)
2025-07-17 23:40:15,465 - INFO - [place_order] - 准备下单: sell SHORT 0.1100 @ 175.8200 (尝试 1/3)
2025-07-17 23:40:15,465 - INFO - [place_order] - 准备下单: sell SHORT 0.1100 @ 175.9700 (尝试 1/3)
2025-07-17 23:40:15,466 - INFO - [place_order] - 准备下单: sell SHORT 0.1100 @ 176.1000 (尝试 1/3)
2025-07-17 23:40:15,466 - INFO - [place_order] - 准备下单: sell SHORT 0.1100 @ 176.1900 (尝试 1/3)
2025-07-17 23:40:15,466 - INFO - [place_order] - 准备下单: sell SHORT 0.1100 @ 176.2600 (尝试 1/3)
2025-07-17 23:40:15,466 - INFO - [place_order] - 准备下单: sell SHORT 0.1100 @ 176.3100 (尝试 1/3)
2025-07-17 23:40:15,467 - INFO - [place_order] - 准备下单: sell SHORT 0.1100 @ 176.3400 (尝试 1/3)
2025-07-17 23:40:15,467 - INFO - [place_order] - 准备下单: sell SHORT 0.1100 @ 176.3700 (尝试 1/3)
2025-07-17 23:40:15,467 - INFO - [place_order] - 准备下单: sell SHORT 0.1100 @ 176.3900 (尝试 1/3)
2025-07-17 23:40:15,468 - INFO - [place_order] - 准备下单: sell SHORT 0.1100 @ 176.4000 (尝试 1/3)
2025-07-17 23:40:15,468 - INFO - [place_order] - 准备下单: sell SHORT 0.1100 @ 176.4100 (尝试 1/3)
2025-07-17 23:40:16,736 - INFO - [place_order] - 下单成功: sell SHORT 0.1100 @ 174.5200, OrderID: 129417609831
2025-07-17 23:40:21,544 - INFO - [place_order] - 下单成功: sell SHORT 0.1100 @ 174.8200, OrderID: 129417627471
2025-07-17 23:40:26,340 - INFO - [place_order] - 下单成功: sell SHORT 0.1100 @ 175.1200, OrderID: 129417648867
2025-07-17 23:40:31,134 - INFO - [place_order] - 下单成功: sell SHORT 0.1100 @ 175.3900, OrderID: 129417662983
2025-07-17 23:40:35,932 - INFO - [place_order] - 下单成功: sell SHORT 0.1100 @ 175.6200, OrderID: 129417683017
2025-07-17 23:40:40,744 - INFO - [place_order] - 下单成功: sell SHORT 0.1100 @ 175.8200, OrderID: 129417696117
2025-07-17 23:40:45,454 - WARNING - [place_order] - 下单超时 (尝试 1/3)
2025-07-17 23:40:45,454 - WARNING - [place_order] - 下单超时 (尝试 1/3)
2025-07-17 23:40:45,455 - WARNING - [place_order] - 下单超时 (尝试 1/3)
2025-07-17 23:40:45,455 - WARNING - [place_order] - 下单超时 (尝试 1/3)
2025-07-17 23:40:45,455 - WARNING - [place_order] - 下单超时 (尝试 1/3)
2025-07-17 23:40:45,455 - WARNING - [place_order] - 下单超时 (尝试 1/3)
2025-07-17 23:40:45,455 - WARNING - [place_order] - 下单超时 (尝试 1/3)
2025-07-17 23:40:45,455 - WARNING - [place_order] - 下单超时 (尝试 1/3)
2025-07-17 23:40:45,455 - WARNING - [place_order] - 下单超时 (尝试 1/3)
2025-07-17 23:40:45,455 - WARNING - [place_order] - 下单超时 (尝试 1/3)
2025-07-17 23:40:46,460 - INFO - [place_order] - 准备下单: sell SHORT 0.1100 @ 176.3700 (尝试 2/3)
2025-07-17 23:40:46,460 - INFO - [place_order] - 准备下单: sell SHORT 0.1100 @ 175.9700 (尝试 2/3)
2025-07-17 23:40:46,460 - INFO - [place_order] - 准备下单: sell SHORT 0.1100 @ 176.3100 (尝试 2/3)
2025-07-17 23:40:46,461 - INFO - [place_order] - 准备下单: sell SHORT 0.1100 @ 176.4000 (尝试 2/3)
2025-07-17 23:40:46,461 - INFO - [place_order] - 准备下单: sell SHORT 0.1100 @ 176.3400 (尝试 2/3)
2025-07-17 23:40:46,461 - INFO - [place_order] - 准备下单: sell SHORT 0.1100 @ 176.3900 (尝试 2/3)
2025-07-17 23:40:46,461 - INFO - [place_order] - 准备下单: sell SHORT 0.1100 @ 176.4100 (尝试 2/3)
2025-07-17 23:40:46,462 - INFO - [place_order] - 准备下单: sell SHORT 0.1100 @ 176.1000 (尝试 2/3)
2025-07-17 23:40:46,462 - INFO - [place_order] - 准备下单: sell SHORT 0.1100 @ 176.2600 (尝试 2/3)
2025-07-17 23:40:46,462 - INFO - [place_order] - 准备下单: sell SHORT 0.1100 @ 176.1900 (尝试 2/3)
2025-07-17 23:41:00,370 - INFO - [close] - 正在关闭程序...
2025-07-17 23:41:00,370 - INFO - [cancel_all_open_orders] - 正在取消 6 个挂单...
2025-07-17 23:41:10,361 - WARNING - [close] - 关闭时取消挂单失败，但程序将继续关闭: 
2025-07-17 23:41:10,620 - INFO - [close] - 程序已关闭。
2025-07-17 23:52:43,374 - INFO - [main] - 启动前预检 API 连接...
2025-07-17 23:52:43,374 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-17 23:52:43,382 - INFO - [main] - 测试代理连接: http://httpbin.org/ip
2025-07-17 23:52:45,806 - INFO - [main] - 代理服务器连接正常 (测试URL: http://httpbin.org/ip)
2025-07-17 23:52:45,807 - INFO - [main] - 代理服务器测试通过
2025-07-17 23:52:45,813 - INFO - [main] - 正在测试 API 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/5
2025-07-17 23:52:45,893 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.08s
2025-07-17 23:52:45,893 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 23:52:45,893 - INFO - [_initialize_exchange] - 已设置代理: http://127.0.0.1:7897
2025-07-17 23:52:45,900 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 23:52:45,900 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 23:52:45,900 - INFO - [setup] - 正在执行启动设置...
2025-07-17 23:52:45,900 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-17 23:52:45,900 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-17 23:52:45,907 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-17 23:52:45,992 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-17 23:52:45,992 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-17 23:52:46,411 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-17 23:52:46,411 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-17 23:52:46,741 - INFO - [test_connection] - 服务器时间获取成功: 1752767566263
2025-07-17 23:52:46,741 - INFO - [test_connection] - 正在加载市场数据...
2025-07-17 23:52:49,330 - INFO - [test_connection] - 市场数据加载成功
2025-07-17 23:52:49,330 - INFO - [test_connection] - 交易对 SOL/USDT:USDT 验证成功
2025-07-17 23:52:49,331 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 23:52:50,588 - INFO - [test_connection] - Ticker 获取成功: SOL/USDT:USDT @ 173.95
2025-07-17 23:52:50,588 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-17 23:52:51,853 - INFO - [test_connection] - 余额获取成功，USDT余额: 1656.93173557
2025-07-17 23:52:51,854 - INFO - [test_connection] - API连接测试完成！
2025-07-17 23:52:51,854 - INFO - [setup] - 正在加载市场数据...
2025-07-17 23:52:54,442 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-17 23:52:55,709 - INFO - [set_hedge_mode] - 当前已是双向持仓模式，无需更改
2025-07-17 23:52:56,974 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-17 23:53:01,837 - WARNING - [full_state_sync] - 挂单数据格式异常: 期望list，实际 <class 'list'>
2025-07-17 23:53:05,567 - INFO - [fetch_listen_key] - 成功获取新的 listen key: QtHdX16DhU...
2025-07-17 23:53:05,567 - INFO - [setup] - 初始化设置完成。
2025-07-17 23:53:05,567 - INFO - [setup] - 当前持仓: 多头=0.0, 空头=0.0
2025-07-17 23:53:09,227 - WARNING - [full_state_sync] - 挂单数据格式异常: 期望list，实际 <class 'list'>
2025-07-17 23:53:09,227 - INFO - [cancel_all_open_orders] - 正在尝试取消所有挂单...
2025-07-17 23:53:10,504 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-17 23:53:10,505 - INFO - [run] - 所有现有挂单已取消，准备启动策略...
2025-07-17 23:53:10,505 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/QtHdX16DhUqt5aA7jIhGigXLdeKcwDw9IErkpbdQiOQ6uNQyVJgGJUHcLUV5LFQ6
2025-07-17 23:53:10,701 - INFO - [run] - WebSocket 连接成功。
2025-07-17 23:53:10,701 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdt@bookTicker'], 'id': 1}
2025-07-17 23:53:14,355 - WARNING - [full_state_sync] - 挂单数据格式异常: 期望list，实际 <class 'list'>
2025-07-17 23:53:14,358 - INFO - [execute_argm_strategy] - 网格重绘触发！新中枢: 174.6394, 新范围: [172.8440, 176.4348]
2025-07-17 23:53:14,359 - INFO - [rebalance_grid_orders] - 准备创建 13 个新订单
2025-07-17 23:53:14,359 - INFO - [place_order] - 准备下单: buy LONG 0.1100 @ 172.8700 (尝试 1/3, 超时 60s)
2025-07-17 23:53:14,359 - INFO - [place_order] - 准备下单: buy LONG 0.1100 @ 172.8800 (尝试 1/3, 超时 60s)
2025-07-17 23:53:14,360 - INFO - [place_order] - 准备下单: buy LONG 0.1100 @ 172.8900 (尝试 1/3, 超时 60s)
2025-07-17 23:53:15,621 - INFO - [place_order] - 下单成功: buy LONG 0.1100 @ 172.8700, OrderID: 129419759636
2025-07-17 23:53:15,621 - INFO - [place_order] - 准备下单: buy LONG 0.1100 @ 172.9100 (尝试 1/3, 超时 60s)
2025-07-17 23:53:20,435 - INFO - [place_order] - 下单成功: buy LONG 0.1100 @ 172.8800, OrderID: 129419765854
2025-07-17 23:53:20,435 - INFO - [place_order] - 准备下单: buy LONG 0.1100 @ 172.9400 (尝试 1/3, 超时 60s)
2025-07-17 23:53:25,225 - INFO - [place_order] - 下单成功: buy LONG 0.1100 @ 172.8900, OrderID: 129419772701
2025-07-17 23:53:25,225 - INFO - [place_order] - 准备下单: buy LONG 0.1100 @ 172.9700 (尝试 1/3, 超时 60s)
2025-07-17 23:53:30,033 - INFO - [place_order] - 下单成功: buy LONG 0.1100 @ 172.9100, OrderID: 129419785330
2025-07-17 23:53:30,033 - INFO - [place_order] - 准备下单: buy LONG 0.1100 @ 173.0300 (尝试 1/3, 超时 60s)
2025-07-17 23:53:34,825 - INFO - [place_order] - 下单成功: buy LONG 0.1100 @ 172.9400, OrderID: 129419802092
2025-07-17 23:53:34,825 - INFO - [place_order] - 准备下单: buy LONG 0.1100 @ 173.1000 (尝试 1/3, 超时 60s)
2025-07-17 23:53:39,623 - INFO - [place_order] - 下单成功: buy LONG 0.1100 @ 172.9700, OrderID: 129419828832
2025-07-17 23:53:39,623 - INFO - [place_order] - 准备下单: buy LONG 0.1100 @ 173.1900 (尝试 1/3, 超时 60s)
2025-07-17 23:53:44,436 - INFO - [place_order] - 下单成功: buy LONG 0.1100 @ 173.0300, OrderID: 129419849705
2025-07-17 23:53:44,436 - INFO - [place_order] - 准备下单: buy LONG 0.1100 @ 173.3100 (尝试 1/3, 超时 60s)
2025-07-17 23:53:49,228 - INFO - [place_order] - 下单成功: buy LONG 0.1100 @ 173.1000, OrderID: 129419865857
2025-07-17 23:53:49,228 - INFO - [place_order] - 准备下单: buy LONG 0.1100 @ 173.4700 (尝试 1/3, 超时 60s)
2025-07-17 23:53:54,021 - INFO - [place_order] - 下单成功: buy LONG 0.1100 @ 173.1900, OrderID: 129419877687
2025-07-17 23:53:54,021 - INFO - [place_order] - 准备下单: buy LONG 0.1100 @ 173.6700 (尝试 1/3, 超时 60s)
2025-07-17 23:53:58,824 - INFO - [place_order] - 下单成功: buy LONG 0.1100 @ 173.3100, OrderID: 129419892129
2025-07-17 23:53:58,824 - INFO - [place_order] - 准备下单: buy LONG 0.1100 @ 173.9100 (尝试 1/3, 超时 60s)
2025-07-17 23:54:03,632 - INFO - [place_order] - 下单成功: buy LONG 0.1100 @ 173.4700, OrderID: 129419912549
2025-07-17 23:54:08,443 - INFO - [place_order] - 下单成功: buy LONG 0.1100 @ 173.6700, OrderID: 129419934355
2025-07-17 23:54:13,238 - INFO - [place_order] - 下单成功: buy LONG 0.1100 @ 173.9100, OrderID: 129419949134
2025-07-17 23:54:13,241 - WARNING - [run] - WebSocket连接已关闭 (1006): 
2025-07-17 23:54:13,241 - INFO - [run] - 等待 5 秒后重连...
2025-07-17 23:54:18,269 - INFO - [close] - 正在关闭程序...
2025-07-17 23:54:18,270 - INFO - [cancel_all_open_orders] - 正在取消 13 个挂单...
2025-07-17 23:54:24,314 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-17 23:54:24,573 - INFO - [close] - 程序已关闭。
2025-07-18 00:07:25,791 - INFO - [main] - 启动前预检 API 连接...
2025-07-18 00:07:25,791 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-18 00:07:25,798 - INFO - [main] - 测试代理连接: http://httpbin.org/ip
2025-07-18 00:10:43,017 - INFO - [main] - 启动前预检 API 连接...
2025-07-18 00:10:43,018 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-18 00:10:43,024 - INFO - [main] - 测试代理连接: http://httpbin.org/ip
2025-07-18 00:10:44,299 - INFO - [main] - 代理服务器连接正常 (测试URL: http://httpbin.org/ip)
2025-07-18 00:10:44,299 - INFO - [main] - 代理服务器测试通过
2025-07-18 00:10:44,305 - INFO - [main] - 正在测试 API 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/5
2025-07-18 00:10:44,390 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.09s
2025-07-18 00:10:44,391 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-18 00:10:44,391 - INFO - [_initialize_exchange] - 已通过 proxyUrl 设置代理: http://127.0.0.1:7897
2025-07-18 00:10:44,398 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-18 00:10:44,398 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-18 00:10:44,400 - INFO - [setup] - 正在执行启动设置...
2025-07-18 00:10:44,400 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-18 00:10:44,400 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-18 00:10:44,405 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-18 00:10:44,489 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-18 00:10:44,490 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-18 00:10:44,797 - WARNING - [test_connection] - CCXT Ping失败，尝试备用方法: Invalid URL: port can't be converted to integer
2025-07-18 00:10:44,806 - ERROR - [test_connection] - 时间API也失败: Invalid URL: port can't be converted to integer
2025-07-18 00:10:44,806 - ERROR - [test_connection] - 连接测试失败 (尝试 1/5): Invalid URL: port can't be converted to integer
2025-07-18 00:10:44,806 - ERROR - [test_connection] - 详细错误信息: ValueError: Invalid URL: port can't be converted to integer
2025-07-18 00:10:44,806 - INFO - [test_connection] - 等待 3 秒后重试...
2025-07-18 00:10:47,811 - INFO - [test_connection] - 正在测试API连接... (尝试 2/5)
2025-07-18 00:10:47,813 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-18 00:10:47,813 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-18 00:10:47,900 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-18 00:10:47,900 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-18 00:10:48,067 - WARNING - [test_connection] - CCXT Ping失败，尝试备用方法: Invalid URL: port can't be converted to integer
2025-07-18 00:10:48,312 - ERROR - [test_connection] - 时间API也失败: Invalid URL: port can't be converted to integer
2025-07-18 00:10:48,313 - ERROR - [test_connection] - 连接测试失败 (尝试 2/5): Invalid URL: port can't be converted to integer
2025-07-18 00:10:48,313 - ERROR - [test_connection] - 详细错误信息: ValueError: Invalid URL: port can't be converted to integer
2025-07-18 00:10:48,313 - INFO - [test_connection] - 等待 3 秒后重试...
2025-07-18 00:10:51,320 - INFO - [close] - 正在关闭程序...
2025-07-18 00:10:51,576 - INFO - [close] - 程序已关闭。
2025-07-18 00:11:39,514 - INFO - [main] - 启动前预检 API 连接...
2025-07-18 00:11:39,515 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-18 00:11:39,522 - INFO - [main] - 测试代理连接: http://httpbin.org/ip
2025-07-18 00:11:54,529 - WARNING - [main] - 代理测试URL http://httpbin.org/ip 失败: Timeout on reading data from socket
2025-07-18 00:11:54,530 - INFO - [main] - 测试代理连接: https://api.binance.com/api/v3/ping
2025-07-18 00:11:54,614 - INFO - [main] - 代理服务器连接正常 (测试URL: https://api.binance.com/api/v3/ping)
2025-07-18 00:11:54,615 - INFO - [main] - 代理服务器测试通过
2025-07-18 00:11:54,620 - INFO - [main] - 正在测试 API 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/5
2025-07-18 00:11:54,704 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.08s
2025-07-18 00:11:54,705 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-18 00:11:54,705 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-07-18 00:11:54,712 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-18 00:11:54,712 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-18 00:11:54,714 - INFO - [setup] - 正在执行启动设置...
2025-07-18 00:11:54,714 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-18 00:11:54,714 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-18 00:11:54,720 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-18 00:11:54,805 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-18 00:11:54,805 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-18 00:11:55,206 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-18 00:11:55,206 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-18 00:11:55,548 - INFO - [test_connection] - 服务器时间获取成功: 1752768715070
2025-07-18 00:11:55,548 - INFO - [test_connection] - 正在加载市场数据...
2025-07-18 00:11:59,574 - INFO - [test_connection] - 市场数据加载成功
2025-07-18 00:11:59,575 - INFO - [test_connection] - 交易对 SOL/USDT:USDT 验证成功
2025-07-18 00:11:59,575 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-18 00:12:00,844 - INFO - [test_connection] - Ticker 获取成功: SOL/USDT:USDT @ 173.13
2025-07-18 00:12:00,844 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-18 00:12:02,104 - INFO - [test_connection] - 余额获取成功，USDT余额: 1656.93173557
2025-07-18 00:12:02,104 - INFO - [test_connection] - API连接测试完成！
2025-07-18 00:12:02,104 - INFO - [setup] - 正在加载市场数据...
2025-07-18 00:12:06,192 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-18 00:12:07,459 - INFO - [set_hedge_mode] - 当前已是双向持仓模式，无需更改
2025-07-18 00:12:08,718 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-18 00:12:13,589 - WARNING - [full_state_sync] - 挂单数据格式异常: 期望list，实际 <class 'list'>
2025-07-18 00:12:17,321 - INFO - [fetch_listen_key] - 成功获取新的 listen key: QtHdX16DhU...
2025-07-18 00:12:17,322 - INFO - [setup] - 初始化设置完成。
2025-07-18 00:12:17,322 - INFO - [setup] - 当前持仓: 多头=0.0, 空头=0.0
2025-07-18 00:12:20,980 - WARNING - [full_state_sync] - 挂单数据格式异常: 期望list，实际 <class 'list'>
2025-07-18 00:12:20,980 - INFO - [cancel_all_open_orders] - 正在尝试取消所有挂单...
2025-07-18 00:12:22,244 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-18 00:12:22,246 - INFO - [run] - 所有现有挂单已取消，准备启动策略...
2025-07-18 00:12:22,246 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/QtHdX16DhUqt5aA7jIhGigXLdeKcwDw9IErkpbdQiOQ6uNQyVJgGJUHcLUV5LFQ6
2025-07-18 00:12:22,459 - INFO - [run] - WebSocket 连接成功。
2025-07-18 00:12:22,459 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdt@bookTicker'], 'id': 1}
2025-07-18 00:12:26,128 - WARNING - [full_state_sync] - 挂单数据格式异常: 期望list，实际 <class 'list'>
2025-07-18 00:12:26,131 - INFO - [execute_argm_strategy] - 网格重绘触发！新中枢: 174.5584, 新范围: [172.7468, 176.3699]
2025-07-18 00:12:26,131 - INFO - [rebalance_grid_orders] - 准备创建 10 个新订单
2025-07-18 00:12:26,131 - INFO - [place_order] - 准备下单: buy LONG 0.1100 @ 172.7700 (尝试 1/3, 超时 60s)
2025-07-18 00:12:26,131 - INFO - [place_order] - 准备下单: buy LONG 0.1100 @ 172.7800 (尝试 1/3, 超时 60s)
2025-07-18 00:12:26,132 - INFO - [place_order] - 准备下单: buy LONG 0.1100 @ 172.7900 (尝试 1/3, 超时 60s)
2025-07-18 00:12:27,407 - INFO - [place_order] - 下单成功: buy LONG 0.1100 @ 172.7700, OrderID: 129423181190
2025-07-18 00:12:27,407 - INFO - [place_order] - 准备下单: buy LONG 0.1100 @ 172.8100 (尝试 1/3, 超时 60s)
2025-07-18 00:12:37,407 - INFO - [close] - 正在关闭程序...
2025-07-18 00:12:37,407 - INFO - [cancel_all_open_orders] - 正在取消 1 个挂单...
2025-07-18 00:12:46,622 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-18 00:12:46,878 - INFO - [close] - 程序已关闭。
2025-07-18 00:13:11,170 - INFO - [main] - 启动前预检 API 连接...
2025-07-18 00:13:11,170 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-18 00:13:11,178 - INFO - [main] - 测试代理连接: http://httpbin.org/ip
2025-07-18 00:13:12,302 - INFO - [main] - 代理服务器连接正常 (测试URL: http://httpbin.org/ip)
2025-07-18 00:13:12,303 - INFO - [main] - 代理服务器测试通过
2025-07-18 00:13:12,308 - INFO - [main] - 正在测试 API 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/5
2025-07-18 00:13:12,392 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.08s
2025-07-18 00:13:12,393 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-18 00:13:12,394 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-07-18 00:13:12,400 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-18 00:13:12,401 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-18 00:13:12,402 - INFO - [setup] - 正在执行启动设置...
2025-07-18 00:13:12,402 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-18 00:13:12,402 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-18 00:13:12,407 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-18 00:13:12,488 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-18 00:13:12,489 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-18 00:13:12,917 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-18 00:13:12,917 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-18 00:13:13,257 - INFO - [test_connection] - 服务器时间获取成功: 1752768792777
2025-07-18 00:13:13,257 - INFO - [test_connection] - 正在加载市场数据...
2025-07-18 00:13:17,289 - INFO - [test_connection] - 市场数据加载成功
2025-07-18 00:13:17,289 - INFO - [test_connection] - 交易对 SOL/USDT:USDT 验证成功
2025-07-18 00:13:17,289 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-18 00:13:18,544 - INFO - [test_connection] - Ticker 获取成功: SOL/USDT:USDT @ 173.2
2025-07-18 00:13:18,544 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-18 00:13:19,811 - INFO - [test_connection] - 余额获取成功，USDT余额: 1656.93173557
2025-07-18 00:13:19,812 - INFO - [test_connection] - API连接测试完成！
2025-07-18 00:13:19,812 - INFO - [setup] - 正在加载市场数据...
2025-07-18 00:13:23,904 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-18 00:13:25,164 - INFO - [set_hedge_mode] - 当前已是双向持仓模式，无需更改
2025-07-18 00:13:26,428 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-18 00:13:31,296 - WARNING - [full_state_sync] - 挂单数据格式异常: 期望list，实际 <class 'list'>
2025-07-18 00:13:31,852 - INFO - [close] - 正在关闭程序...
2025-07-18 00:13:31,852 - INFO - [cancel_all_open_orders] - 正在尝试取消所有挂单...
2025-07-18 00:13:32,213 - WARNING - [__del__] - binanceusdm requires to release all resources with an explicit call to the .close() coroutine. If you are using the exchange instance with async coroutines, add `await exchange.close()` to your code into a place when you're done with the exchange and don't need the exchange instance anymore (at the end of your async coroutine).
2025-07-18 00:13:32,213 - ERROR - [default_exception_handler] - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x0000023B3D07DFD0>, 2320998.015)])', 'deque([(<aiohttp.client_proto.ResponseHandler object at 0x0000023B3D07E5D0>, 2321007.0)])']
connector: <aiohttp.connector.TCPConnector object at 0x0000023B3C9ACE60>
2025-07-18 00:14:25,015 - INFO - [main] - 启动前预检 API 连接...
2025-07-18 00:14:25,015 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-18 00:14:25,024 - INFO - [main] - 测试代理连接: http://httpbin.org/ip
2025-07-18 00:14:27,058 - INFO - [main] - 代理服务器连接正常 (测试URL: http://httpbin.org/ip)
2025-07-18 00:14:27,058 - INFO - [main] - 代理服务器测试通过
2025-07-18 00:14:27,064 - INFO - [main] - 正在测试 API 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/5
2025-07-18 00:14:27,149 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.09s
2025-07-18 00:14:27,150 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-18 00:14:27,151 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-07-18 00:14:27,157 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-18 00:14:27,158 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-18 00:14:27,159 - INFO - [setup] - 正在执行启动设置...
2025-07-18 00:14:27,159 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-18 00:14:27,159 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-18 00:14:27,165 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-18 00:14:27,244 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-18 00:14:27,244 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-18 00:14:27,641 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-18 00:14:27,642 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-18 00:14:27,981 - INFO - [test_connection] - 服务器时间获取成功: 1752768867501
2025-07-18 00:14:27,981 - INFO - [test_connection] - 正在加载市场数据...
2025-07-18 00:14:31,760 - INFO - [close] - 正在关闭程序...
2025-07-18 00:14:32,004 - INFO - [close] - 程序已关闭。
2025-07-18 00:16:38,780 - INFO - [main] - 启动前预检 API 连接...
2025-07-18 00:16:38,780 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-18 00:16:38,787 - INFO - [main] - 测试代理连接: http://httpbin.org/ip
2025-07-18 00:16:50,258 - INFO - [main] - 代理服务器连接正常 (测试URL: http://httpbin.org/ip)
2025-07-18 00:16:50,259 - INFO - [main] - 代理服务器测试通过
2025-07-18 00:16:50,264 - INFO - [main] - 正在测试 API 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/5
2025-07-18 00:16:50,348 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.08s
2025-07-18 00:16:50,349 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-18 00:16:50,350 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-07-18 00:16:50,356 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-18 00:16:50,356 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-18 00:16:50,358 - INFO - [setup] - 正在执行启动设置...
2025-07-18 00:16:50,358 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-18 00:16:50,358 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-18 00:16:50,363 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-18 00:16:50,446 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-18 00:16:50,447 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-18 00:16:50,848 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-18 00:16:50,848 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-18 00:16:51,184 - INFO - [test_connection] - 服务器时间获取成功: 1752769010705
2025-07-18 00:16:51,185 - INFO - [test_connection] - 正在加载市场数据...
2025-07-18 00:16:55,218 - INFO - [test_connection] - 市场数据加载成功
2025-07-18 00:16:55,218 - INFO - [test_connection] - 交易对 SOL/USDT:USDT 验证成功
2025-07-18 00:16:55,218 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-18 00:16:56,476 - INFO - [test_connection] - Ticker 获取成功: SOL/USDT:USDT @ 172.77
2025-07-18 00:16:56,476 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-18 00:16:57,750 - INFO - [test_connection] - 余额获取成功，USDT余额: 1656.93173557
2025-07-18 00:16:57,750 - INFO - [test_connection] - API连接测试完成！
2025-07-18 00:16:57,750 - INFO - [setup] - 正在加载市场数据...
2025-07-18 00:17:01,834 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-18 00:17:03,106 - INFO - [set_hedge_mode] - 当前已是双向持仓模式，无需更改
2025-07-18 00:17:04,369 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-18 00:17:13,079 - INFO - [fetch_listen_key] - 成功获取新的 listen key: QtHdX16DhU...
2025-07-18 00:17:13,079 - INFO - [setup] - 初始化设置完成。
2025-07-18 00:17:13,079 - INFO - [setup] - 当前持仓: 多头=0.0, 空头=0.0
2025-07-18 00:17:16,746 - INFO - [cancel_all_open_orders] - 正在尝试取消所有挂单...
2025-07-18 00:17:18,107 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-18 00:17:18,107 - INFO - [run] - 所有现有挂单已取消，准备启动策略...
2025-07-18 00:17:18,107 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/QtHdX16DhUqt5aA7jIhGigXLdeKcwDw9IErkpbdQiOQ6uNQyVJgGJUHcLUV5LFQ6
2025-07-18 00:17:18,305 - INFO - [run] - WebSocket 连接成功。
2025-07-18 00:17:18,306 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdt@bookTicker'], 'id': 1}
2025-07-18 00:17:21,975 - INFO - [execute_argm_strategy] - 网格重绘触发！新中枢: 167.1844, 新范围: [161.2321, 173.1366]
2025-07-18 00:17:21,976 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:22,485 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:22,988 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:23,491 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:23,993 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:24,496 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:25,005 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:25,508 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:26,009 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:26,511 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:27,024 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:27,528 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:28,038 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:28,540 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:29,040 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:29,546 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:30,046 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:30,548 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:31,049 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:31,567 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:32,068 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:32,579 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:33,106 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:33,629 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:34,129 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:34,635 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:35,204 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:35,708 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:36,373 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:36,883 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:37,409 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:37,937 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:38,441 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:38,966 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:39,489 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:39,990 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:40,492 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:40,994 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:41,505 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:17:42,009 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:17:42,511 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:17:43,033 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:17:43,534 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:17:44,035 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:17:44,539 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:17:45,046 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:17:45,568 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:17:46,082 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:17:46,630 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:17:47,175 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:17:47,680 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:17:48,189 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:17:48,705 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:17:49,207 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:17:49,733 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:17:50,264 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:17:50,764 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:17:51,311 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:17:51,820 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:17:52,330 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:17:52,832 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:17:53,335 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:17:53,851 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:17:54,378 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:17:54,887 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:17:55,389 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:17:55,930 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:17:56,460 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:17:56,976 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:17:57,476 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:17:57,977 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:17:58,538 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:17:59,050 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:17:59,602 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:18:00,189 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:18:00,693 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:18:01,194 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:18:01,703 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:18:02,206 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:18:02,728 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:18:03,242 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:18:03,764 - INFO - [rebalance_grid_orders] - 准备创建 28 个新订单
2025-07-18 00:18:04,267 - INFO - [rebalance_grid_orders] - 准备创建 28 个新订单
2025-07-18 00:18:04,767 - INFO - [rebalance_grid_orders] - 准备创建 29 个新订单
2025-07-18 00:18:05,269 - INFO - [rebalance_grid_orders] - 准备创建 29 个新订单
2025-07-18 00:18:05,773 - INFO - [rebalance_grid_orders] - 准备创建 29 个新订单
2025-07-18 00:18:06,275 - INFO - [rebalance_grid_orders] - 准备创建 29 个新订单
2025-07-18 00:18:06,792 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:18:07,489 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:18:08,000 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:18:08,508 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:18:09,009 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:18:09,529 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:18:10,030 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:18:18,633 - INFO - [close] - 正在关闭程序...
2025-07-18 00:18:18,634 - INFO - [cancel_all_open_orders] - 正在尝试取消所有挂单...
2025-07-18 00:18:19,915 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-18 00:18:20,180 - INFO - [close] - 程序已关闭。
2025-07-18 00:21:17,035 - INFO - [main] - 启动前预检 API 连接...
2025-07-18 00:21:17,035 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-18 00:21:17,041 - INFO - [main] - 测试代理连接: http://httpbin.org/ip
2025-07-18 00:21:22,991 - INFO - [main] - 代理服务器连接正常 (测试URL: http://httpbin.org/ip)
2025-07-18 00:21:22,992 - INFO - [main] - 代理服务器测试通过
2025-07-18 00:21:22,996 - INFO - [main] - 正在测试 API 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/5
2025-07-18 00:21:23,087 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.09s
2025-07-18 00:21:23,088 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-18 00:21:23,088 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-07-18 00:21:23,095 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-18 00:21:23,095 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-18 00:21:23,096 - INFO - [setup] - 正在执行启动设置...
2025-07-18 00:21:23,097 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-18 00:21:23,097 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-18 00:21:23,101 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-18 00:21:23,181 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-18 00:21:23,182 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-18 00:21:23,591 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-18 00:21:23,591 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-18 00:21:23,923 - INFO - [test_connection] - 服务器时间获取成功: 1752769283444
2025-07-18 00:21:23,924 - INFO - [test_connection] - 正在加载市场数据...
2025-07-18 00:21:27,947 - INFO - [test_connection] - 市场数据加载成功
2025-07-18 00:21:27,947 - INFO - [test_connection] - 交易对 SOL/USDT:USDT 验证成功
2025-07-18 00:21:27,947 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-18 00:21:29,220 - INFO - [test_connection] - Ticker 获取成功: SOL/USDT:USDT @ 172.98
2025-07-18 00:21:29,220 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-18 00:21:30,479 - INFO - [test_connection] - 余额获取成功，USDT余额: 1656.93173557
2025-07-18 00:21:30,479 - INFO - [test_connection] - API连接测试完成！
2025-07-18 00:21:30,479 - INFO - [setup] - 正在加载市场数据...
2025-07-18 00:21:34,565 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-18 00:21:35,872 - INFO - [set_hedge_mode] - 当前已是双向持仓模式，无需更改
2025-07-18 00:21:37,143 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-18 00:21:45,733 - INFO - [fetch_listen_key] - 成功获取新的 listen key: QtHdX16DhU...
2025-07-18 00:21:45,734 - INFO - [setup] - 初始化设置完成。
2025-07-18 00:21:45,734 - INFO - [setup] - 当前持仓: 多头=0.0, 空头=0.0
2025-07-18 00:21:49,396 - INFO - [cancel_all_open_orders] - 正在尝试取消所有挂单...
2025-07-18 00:21:50,672 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-18 00:21:50,672 - INFO - [run] - 所有现有挂单已取消，准备启动策略...
2025-07-18 00:21:50,672 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/QtHdX16DhUqt5aA7jIhGigXLdeKcwDw9IErkpbdQiOQ6uNQyVJgGJUHcLUV5LFQ6
2025-07-18 00:21:50,871 - INFO - [run] - WebSocket 连接成功。
2025-07-18 00:21:50,872 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdt@bookTicker'], 'id': 1}
2025-07-18 00:21:54,526 - INFO - [execute_argm_strategy] - 网格重绘触发！新中枢: 167.1902, 新范围: [161.1676, 173.2127]
2025-07-18 00:21:54,526 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:21:55,028 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:21:55,576 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:21:56,095 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:21:56,603 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:21:57,113 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:21:57,617 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:21:58,188 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:21:58,689 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:21:59,197 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:21:59,708 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:00,223 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:00,725 - INFO - [rebalance_grid_orders] - 准备创建 24 个新订单
2025-07-18 00:22:01,228 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:01,733 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:02,247 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:02,750 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:03,252 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:03,756 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:04,268 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:04,774 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:05,317 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:05,822 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:06,322 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:06,831 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:07,337 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:07,841 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:08,348 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:08,863 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:09,426 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:09,947 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:10,464 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:10,967 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:11,469 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:11,969 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:12,472 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:12,976 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:13,481 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:13,986 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:14,497 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:15,004 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:15,505 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:16,008 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:16,509 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:17,009 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:17,539 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:18,052 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:18,586 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:19,104 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:19,617 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:20,152 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:20,689 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:21,205 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:21,712 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:22,218 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:22,719 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:23,364 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:23,875 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:24,400 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:24,903 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:25,403 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:25,919 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:26,420 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:26,923 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:27,423 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:27,922 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:28,428 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:28,928 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:29,461 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:29,977 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:30,497 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:31,007 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:31,510 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:32,012 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:32,684 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:33,206 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:33,718 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:34,222 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:34,723 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:35,232 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:22:35,740 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:22:36,252 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:22:36,800 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:22:37,301 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:22:37,811 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:38,310 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:38,812 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:39,316 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:39,826 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:40,373 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:40,873 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:41,387 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:41,916 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:42,420 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:42,932 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:43,488 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:44,013 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:44,513 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:22:45,028 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:22:45,540 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:22:46,055 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:22:46,562 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:22:47,062 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:22:47,584 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:22:48,158 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:22:48,671 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:22:49,177 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:22:49,678 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:22:50,195 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:22:50,700 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:22:51,249 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:22:51,757 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:22:52,262 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:22:52,763 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:22:53,265 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:22:53,768 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:22:54,281 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:22:59,711 - ERROR - [update_price_series] - 更新K线数据失败 (尝试 1/3): K线数据不足: 需要200条，获得178条
2025-07-18 00:23:03,171 - INFO - [execute_argm_strategy] - 网格重绘触发！新中枢: 167.1910, 新范围: [161.1582, 173.2238]
2025-07-18 00:23:03,172 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:23:03,173 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:23:03,673 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:23:04,358 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:23:04,857 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:23:05,407 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:23:05,973 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:23:06,495 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:23:07,001 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:23:07,504 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:23:08,014 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:23:08,527 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:23:09,037 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:23:09,547 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:23:10,049 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:23:10,564 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:23:11,065 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:23:11,569 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:23:12,072 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:23:12,660 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:23:13,164 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:23:13,693 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:23:14,191 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:23:14,700 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:23:15,213 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:23:15,714 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:23:16,309 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:23:16,812 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:23:17,386 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:23:17,891 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:23:18,394 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:23:18,896 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:23:19,418 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:23:19,936 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:23:20,436 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:23:20,959 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:23:21,495 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:23:21,995 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:23:22,497 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:23:23,003 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:23:23,505 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:23:24,007 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:23:30,709 - INFO - [close] - 正在关闭程序...
2025-07-18 00:23:30,709 - INFO - [cancel_all_open_orders] - 正在尝试取消所有挂单...
2025-07-18 00:23:33,006 - ERROR - [default_exception_handler] - Task was destroyed but it is pending!
task: <Task pending name='Task-4099' coro=<Throttler.looper() done, defined at C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\throttler.py:21> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-07-18 00:23:33,043 - ERROR - [default_exception_handler] - Task was destroyed but it is pending!
task: <Task cancelling name='Task-1' coro=<main() done, defined at C:\Users\<USER>\Desktop\grid_trate\fuli_BN.py:1660> wait_for=<Future pending cb=[Task.task_wakeup()]> cb=[gather.<locals>._done_callback() at C:\Python312\Lib\asyncio\tasks.py:767]>
2025-07-18 00:23:33,043 - WARNING - [__del__] - binanceusdm requires to release all resources with an explicit call to the .close() coroutine. If you are using the exchange instance with async coroutines, add `await exchange.close()` to your code into a place when you're done with the exchange and don't need the exchange instance anymore (at the end of your async coroutine).
2025-07-18 00:23:33,043 - ERROR - [default_exception_handler] - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000012F92F865A0>
2025-07-18 00:23:35,229 - INFO - [main] - 启动前预检 API 连接...
2025-07-18 00:23:35,230 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-18 00:23:35,238 - INFO - [main] - 测试代理连接: http://httpbin.org/ip
2025-07-18 00:23:36,945 - INFO - [main] - 代理服务器连接正常 (测试URL: http://httpbin.org/ip)
2025-07-18 00:23:36,945 - INFO - [main] - 代理服务器测试通过
2025-07-18 00:23:36,951 - INFO - [main] - 正在测试 API 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/5
2025-07-18 00:23:37,035 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.08s
2025-07-18 00:23:37,035 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-18 00:23:37,036 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-07-18 00:23:37,043 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-18 00:23:37,043 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-18 00:23:37,045 - INFO - [setup] - 正在执行启动设置...
2025-07-18 00:23:37,045 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-18 00:23:37,045 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-18 00:23:37,050 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-18 00:23:37,134 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-18 00:23:37,135 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-18 00:23:37,543 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-18 00:23:37,544 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-18 00:23:37,877 - INFO - [test_connection] - 服务器时间获取成功: 1752769417398
2025-07-18 00:23:37,878 - INFO - [test_connection] - 正在加载市场数据...
2025-07-18 00:23:41,905 - INFO - [test_connection] - 市场数据加载成功
2025-07-18 00:23:41,905 - INFO - [test_connection] - 交易对 SOL/USDT:USDT 验证成功
2025-07-18 00:23:41,905 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-18 00:23:43,174 - INFO - [test_connection] - Ticker 获取成功: SOL/USDT:USDT @ 172.99
2025-07-18 00:23:43,174 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-18 00:23:44,441 - INFO - [test_connection] - 余额获取成功，USDT余额: 1656.93173557
2025-07-18 00:23:44,441 - INFO - [test_connection] - API连接测试完成！
2025-07-18 00:23:44,441 - INFO - [setup] - 正在加载市场数据...
2025-07-18 00:23:48,557 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-18 00:23:49,829 - INFO - [set_hedge_mode] - 当前已是双向持仓模式，无需更改
2025-07-18 00:23:51,097 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-18 00:23:59,703 - INFO - [fetch_listen_key] - 成功获取新的 listen key: QtHdX16DhU...
2025-07-18 00:23:59,703 - INFO - [setup] - 初始化设置完成。
2025-07-18 00:23:59,703 - INFO - [setup] - 当前持仓: 多头=0.0, 空头=0.0
2025-07-18 00:24:03,373 - INFO - [cancel_all_open_orders] - 正在尝试取消所有挂单...
2025-07-18 00:24:04,631 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-18 00:24:04,631 - INFO - [run] - 所有现有挂单已取消，准备启动策略...
2025-07-18 00:24:04,631 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/QtHdX16DhUqt5aA7jIhGigXLdeKcwDw9IErkpbdQiOQ6uNQyVJgGJUHcLUV5LFQ6
2025-07-18 00:24:04,830 - INFO - [run] - WebSocket 连接成功。
2025-07-18 00:24:04,830 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdt@bookTicker'], 'id': 1}
2025-07-18 00:24:08,492 - INFO - [execute_argm_strategy] - 网格重绘触发！新中枢: 167.1908, 新范围: [161.1605, 173.2210]
2025-07-18 00:24:08,493 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:24:09,001 - INFO - [rebalance_grid_orders] - 准备创建 28 个新订单
2025-07-18 00:24:09,525 - INFO - [rebalance_grid_orders] - 准备创建 28 个新订单
2025-07-18 00:24:10,041 - INFO - [rebalance_grid_orders] - 准备创建 28 个新订单
2025-07-18 00:24:10,582 - INFO - [rebalance_grid_orders] - 准备创建 28 个新订单
2025-07-18 00:24:11,084 - INFO - [rebalance_grid_orders] - 准备创建 29 个新订单
2025-07-18 00:24:11,584 - INFO - [rebalance_grid_orders] - 准备创建 29 个新订单
2025-07-18 00:24:12,126 - INFO - [rebalance_grid_orders] - 准备创建 29 个新订单
2025-07-18 00:24:12,645 - INFO - [rebalance_grid_orders] - 准备创建 29 个新订单
2025-07-18 00:24:13,187 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:24:13,688 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:24:14,230 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:24:14,737 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:24:15,239 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:24:15,741 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:24:16,400 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:24:16,970 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:24:17,480 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:24:17,989 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:24:18,489 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:24:18,989 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:24:19,492 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:24:20,077 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:24:20,589 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:24:21,236 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:24:21,739 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:24:22,253 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:24:22,753 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:24:23,253 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:24:23,777 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:24:24,343 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:24:24,858 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:24:25,364 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:24:25,868 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:24:26,506 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:24:27,022 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:24:27,545 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:24:28,059 - INFO - [rebalance_grid_orders] - 准备创建 29 个新订单
2025-07-18 00:24:28,561 - INFO - [rebalance_grid_orders] - 准备创建 29 个新订单
2025-07-18 00:24:29,102 - INFO - [rebalance_grid_orders] - 准备创建 29 个新订单
2025-07-18 00:24:29,604 - INFO - [rebalance_grid_orders] - 准备创建 28 个新订单
2025-07-18 00:24:30,106 - INFO - [rebalance_grid_orders] - 准备创建 28 个新订单
2025-07-18 00:24:30,652 - INFO - [rebalance_grid_orders] - 准备创建 28 个新订单
2025-07-18 00:24:31,188 - INFO - [rebalance_grid_orders] - 准备创建 28 个新订单
2025-07-18 00:24:31,689 - INFO - [rebalance_grid_orders] - 准备创建 28 个新订单
2025-07-18 00:24:32,210 - INFO - [rebalance_grid_orders] - 准备创建 28 个新订单
2025-07-18 00:24:32,717 - INFO - [rebalance_grid_orders] - 准备创建 28 个新订单
2025-07-18 00:24:33,243 - INFO - [rebalance_grid_orders] - 准备创建 28 个新订单
2025-07-18 00:24:33,765 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:24:34,285 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:24:34,785 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:24:35,286 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:24:35,788 - INFO - [rebalance_grid_orders] - 准备创建 28 个新订单
2025-07-18 00:24:36,293 - INFO - [rebalance_grid_orders] - 准备创建 28 个新订单
2025-07-18 00:24:36,804 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:24:37,305 - INFO - [rebalance_grid_orders] - 准备创建 28 个新订单
2025-07-18 00:24:37,820 - INFO - [rebalance_grid_orders] - 准备创建 28 个新订单
2025-07-18 00:24:38,320 - INFO - [rebalance_grid_orders] - 准备创建 28 个新订单
2025-07-18 00:24:38,836 - INFO - [rebalance_grid_orders] - 准备创建 28 个新订单
2025-07-18 00:24:39,346 - INFO - [rebalance_grid_orders] - 准备创建 28 个新订单
2025-07-18 00:24:39,875 - INFO - [rebalance_grid_orders] - 准备创建 28 个新订单
2025-07-18 00:24:40,389 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:24:40,890 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:24:41,403 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:24:41,912 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:24:42,427 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:24:42,979 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:24:43,480 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:24:43,983 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:24:44,486 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:24:45,001 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:24:45,562 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:24:46,064 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:24:46,605 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:24:47,167 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:24:47,670 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:24:48,252 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:24:48,752 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:24:49,258 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:24:49,766 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:24:50,265 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:24:50,766 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:24:51,341 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:24:51,856 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:24:52,355 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:24:52,959 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:24:53,480 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:24:54,014 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:24:54,536 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:24:55,038 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:24:55,538 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:24:56,062 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:24:56,563 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:24:57,085 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:24:57,590 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:24:58,099 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:24:58,622 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:24:59,126 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:24:59,636 - INFO - [rebalance_grid_orders] - 准备创建 26 个新订单
2025-07-18 00:25:04,838 - INFO - [close] - 正在关闭程序...
2025-07-18 00:25:04,839 - INFO - [cancel_all_open_orders] - 正在尝试取消所有挂单...
2025-07-18 00:25:06,138 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-18 00:25:06,394 - INFO - [close] - 程序已关闭。
2025-07-18 00:26:05,848 - INFO - [main] - 启动前预检 API 连接...
2025-07-18 00:26:05,849 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-18 00:26:05,855 - INFO - [main] - 测试代理连接: http://httpbin.org/ip
2025-07-18 00:26:14,960 - INFO - [main] - 代理服务器连接正常 (测试URL: http://httpbin.org/ip)
2025-07-18 00:26:14,960 - INFO - [main] - 代理服务器测试通过
2025-07-18 00:26:14,965 - INFO - [main] - 正在测试 API 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/5
2025-07-18 00:26:15,050 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.09s
2025-07-18 00:26:15,051 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-18 00:26:15,051 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-07-18 00:26:15,058 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-18 00:26:15,058 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-18 00:26:15,060 - INFO - [setup] - 正在执行启动设置...
2025-07-18 00:26:15,060 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-18 00:26:15,060 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-18 00:26:15,065 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-18 00:26:15,150 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-18 00:26:15,151 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-18 00:26:15,558 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-18 00:26:15,558 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-18 00:26:15,897 - INFO - [test_connection] - 服务器时间获取成功: 1752769575418
2025-07-18 00:26:15,898 - INFO - [test_connection] - 正在加载市场数据...
2025-07-18 00:26:20,078 - INFO - [test_connection] - 市场数据加载成功
2025-07-18 00:26:20,078 - INFO - [test_connection] - 交易对 SOL/USDT:USDT 验证成功
2025-07-18 00:26:20,078 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-18 00:26:21,330 - INFO - [test_connection] - Ticker 获取成功: SOL/USDT:USDT @ 172.9
2025-07-18 00:26:21,331 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-18 00:26:22,598 - INFO - [test_connection] - 余额获取成功，USDT余额: 1656.93173557
2025-07-18 00:26:22,598 - INFO - [test_connection] - API连接测试完成！
2025-07-18 00:26:22,598 - INFO - [setup] - 正在加载市场数据...
2025-07-18 00:26:26,680 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-18 00:26:27,942 - INFO - [set_hedge_mode] - 当前已是双向持仓模式，无需更改
2025-07-18 00:26:29,200 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-18 00:26:37,796 - INFO - [fetch_listen_key] - 成功获取新的 listen key: QtHdX16DhU...
2025-07-18 00:26:37,796 - INFO - [setup] - 初始化设置完成。
2025-07-18 00:26:37,796 - INFO - [setup] - 当前持仓: 多头=0.0, 空头=0.0
2025-07-18 00:26:41,460 - INFO - [cancel_all_open_orders] - 正在尝试取消所有挂单...
2025-07-18 00:26:42,730 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-18 00:26:42,731 - INFO - [run] - 所有现有挂单已取消，准备启动策略...
2025-07-18 00:26:42,731 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/QtHdX16DhUqt5aA7jIhGigXLdeKcwDw9IErkpbdQiOQ6uNQyVJgGJUHcLUV5LFQ6
2025-07-18 00:26:42,928 - INFO - [run] - WebSocket 连接成功。
2025-07-18 00:26:42,928 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdt@bookTicker'], 'id': 1}
2025-07-18 00:26:46,592 - INFO - [execute_argm_strategy] - 网格重绘触发！新中枢: 167.1848, 新范围: [161.2276, 173.1420]
2025-07-18 00:26:46,592 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:26:47,102 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:26:47,644 - INFO - [rebalance_grid_orders] - 准备创建 27 个新订单
2025-07-18 00:26:48,156 - INFO - [rebalance_grid_orders] - 准备创建 28 个新订单
2025-07-18 00:26:48,665 - INFO - [rebalance_grid_orders] - 准备创建 28 个新订单
2025-07-18 00:26:49,174 - INFO - [rebalance_grid_orders] - 准备创建 28 个新订单
2025-07-18 00:26:49,677 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:26:50,179 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:26:50,689 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:26:51,196 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:26:51,699 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:26:52,206 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:26:52,707 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:26:53,207 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:26:53,707 - INFO - [rebalance_grid_orders] - 准备创建 30 个新订单
2025-07-18 00:27:01,306 - INFO - [close] - 正在关闭程序...
2025-07-18 00:27:01,306 - INFO - [cancel_all_open_orders] - 正在尝试取消所有挂单...
2025-07-18 00:27:02,585 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-18 00:27:02,847 - INFO - [close] - 程序已关闭。
2025-07-18 00:31:06,268 - INFO - [main] - 启动前预检 API 连接...
2025-07-18 00:31:06,268 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-18 00:31:06,276 - INFO - [main] - 测试代理连接: http://httpbin.org/ip
2025-07-18 00:31:09,042 - INFO - [main] - 代理服务器连接正常 (测试URL: http://httpbin.org/ip)
2025-07-18 00:31:09,042 - INFO - [main] - 代理服务器测试通过
2025-07-18 00:31:09,048 - INFO - [main] - 正在测试 API 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/5
2025-07-18 00:31:09,131 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.08s
2025-07-18 00:31:09,132 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-18 00:31:09,132 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-07-18 00:31:09,140 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-18 00:31:09,140 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-18 00:31:09,142 - INFO - [setup] - 正在执行启动设置...
2025-07-18 00:31:09,142 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-18 00:31:09,142 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-18 00:31:09,146 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-18 00:31:09,227 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-18 00:31:09,228 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-18 00:31:09,663 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-18 00:31:09,664 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-18 00:31:09,993 - INFO - [test_connection] - 服务器时间获取成功: 1752769869514
2025-07-18 00:31:09,993 - INFO - [test_connection] - 正在加载市场数据...
2025-07-18 00:31:14,017 - INFO - [test_connection] - 市场数据加载成功
2025-07-18 00:31:14,017 - INFO - [test_connection] - 交易对 SOL/USDT:USDT 验证成功
2025-07-18 00:31:14,017 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-18 00:31:15,272 - INFO - [test_connection] - Ticker 获取成功: SOL/USDT:USDT @ 171.78
2025-07-18 00:31:15,272 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-18 00:31:16,542 - INFO - [test_connection] - 余额获取成功，USDT余额: 1656.93173557
2025-07-18 00:31:16,542 - INFO - [test_connection] - API连接测试完成！
2025-07-18 00:31:16,543 - INFO - [setup] - 正在加载市场数据...
2025-07-18 00:31:20,642 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-18 00:31:21,903 - INFO - [set_hedge_mode] - 当前已是双向持仓模式，无需更改
2025-07-18 00:31:23,162 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-18 00:31:31,756 - INFO - [fetch_listen_key] - 成功获取新的 listen key: QtHdX16DhU...
2025-07-18 00:31:31,757 - INFO - [setup] - 初始化设置完成。
2025-07-18 00:31:31,757 - INFO - [setup] - 当前持仓: 多头=0.0, 空头=0.0
2025-07-18 00:31:35,428 - INFO - [cancel_all_open_orders] - 正在尝试取消所有挂单...
2025-07-18 00:31:36,709 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-18 00:31:36,710 - INFO - [run] - 所有现有挂单已取消，准备启动策略...
2025-07-18 00:31:36,710 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/QtHdX16DhUqt5aA7jIhGigXLdeKcwDw9IErkpbdQiOQ6uNQyVJgGJUHcLUV5LFQ6
2025-07-18 00:31:36,909 - INFO - [run] - WebSocket 连接成功。
2025-07-18 00:31:36,909 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdt@bookTicker'], 'id': 1}
2025-07-18 00:31:40,565 - INFO - [execute_argm_strategy] - 网格重绘触发！新中枢: 167.1638, 新范围: [160.9196, 173.4079]
2025-07-18 00:31:40,565 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:31:40,565 - INFO - [rebalance_grid_orders] - 准备创建 21 个新订单
2025-07-18 00:31:42,592 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:31:42,592 - INFO - [rebalance_grid_orders] - 准备创建 21 个新订单
2025-07-18 00:31:44,629 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:31:44,630 - INFO - [rebalance_grid_orders] - 准备创建 21 个新订单
2025-07-18 00:31:46,780 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:31:46,781 - INFO - [rebalance_grid_orders] - 准备创建 21 个新订单
2025-07-18 00:31:48,788 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:31:48,788 - INFO - [rebalance_grid_orders] - 准备创建 21 个新订单
2025-07-18 00:31:50,811 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:31:50,811 - INFO - [rebalance_grid_orders] - 准备创建 21 个新订单
2025-07-18 00:31:52,879 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:31:52,879 - INFO - [rebalance_grid_orders] - 准备创建 21 个新订单
2025-07-18 00:31:54,898 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:31:54,899 - INFO - [rebalance_grid_orders] - 准备创建 21 个新订单
2025-07-18 00:31:56,935 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:31:56,935 - INFO - [rebalance_grid_orders] - 准备创建 21 个新订单
2025-07-18 00:31:58,957 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:31:58,958 - INFO - [rebalance_grid_orders] - 准备创建 21 个新订单
2025-07-18 00:32:00,993 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:32:00,994 - INFO - [rebalance_grid_orders] - 准备创建 21 个新订单
2025-07-18 00:32:03,032 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:32:03,032 - INFO - [rebalance_grid_orders] - 准备创建 21 个新订单
2025-07-18 00:32:05,052 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:32:05,052 - INFO - [rebalance_grid_orders] - 准备创建 21 个新订单
2025-07-18 00:32:07,084 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:32:07,084 - INFO - [rebalance_grid_orders] - 准备创建 21 个新订单
2025-07-18 00:32:09,099 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:32:09,099 - INFO - [rebalance_grid_orders] - 准备创建 21 个新订单
2025-07-18 00:32:11,123 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:32:11,123 - INFO - [rebalance_grid_orders] - 准备创建 21 个新订单
2025-07-18 00:32:13,140 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:32:13,140 - INFO - [rebalance_grid_orders] - 准备创建 21 个新订单
2025-07-18 00:32:15,168 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:32:15,168 - INFO - [rebalance_grid_orders] - 准备创建 21 个新订单
2025-07-18 00:32:17,328 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:32:17,329 - INFO - [rebalance_grid_orders] - 准备创建 21 个新订单
2025-07-18 00:32:19,345 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:32:19,345 - INFO - [rebalance_grid_orders] - 准备创建 21 个新订单
2025-07-18 00:32:21,389 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:32:21,390 - INFO - [rebalance_grid_orders] - 准备创建 21 个新订单
2025-07-18 00:32:23,414 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:32:23,415 - INFO - [rebalance_grid_orders] - 准备创建 21 个新订单
2025-07-18 00:32:25,442 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:32:25,442 - INFO - [rebalance_grid_orders] - 准备创建 21 个新订单
2025-07-18 00:32:27,542 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:32:27,542 - INFO - [rebalance_grid_orders] - 准备创建 21 个新订单
2025-07-18 00:32:29,669 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:32:29,670 - INFO - [rebalance_grid_orders] - 准备创建 21 个新订单
2025-07-18 00:32:31,712 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:32:31,712 - INFO - [rebalance_grid_orders] - 准备创建 21 个新订单
2025-07-18 00:32:33,725 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:32:33,725 - INFO - [rebalance_grid_orders] - 准备创建 20 个新订单
2025-07-18 00:32:36,911 - INFO - [close] - 正在关闭程序...
2025-07-18 00:32:36,911 - INFO - [cancel_all_open_orders] - 正在尝试取消所有挂单...
2025-07-18 00:32:38,219 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-18 00:32:38,473 - INFO - [close] - 程序已关闭。
2025-07-18 00:40:58,383 - INFO - [main] - 启动前预检 API 连接...
2025-07-18 00:40:58,384 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-18 00:40:58,391 - INFO - [main] - 测试代理连接: http://httpbin.org/ip
2025-07-18 00:40:59,914 - INFO - [main] - 代理服务器连接正常 (测试URL: http://httpbin.org/ip)
2025-07-18 00:40:59,915 - INFO - [main] - 代理服务器测试通过
2025-07-18 00:40:59,920 - INFO - [main] - 正在测试 API 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/5
2025-07-18 00:41:00,003 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.08s
2025-07-18 00:41:00,003 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-18 00:41:00,003 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-07-18 00:41:00,010 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-18 00:41:00,010 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-18 00:41:00,012 - INFO - [setup] - 正在执行启动设置...
2025-07-18 00:41:00,012 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-18 00:41:00,012 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-18 00:41:00,018 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-18 00:41:00,102 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-18 00:41:00,103 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-18 00:41:00,526 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-18 00:41:00,526 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-18 00:41:00,873 - INFO - [test_connection] - 服务器时间获取成功: 1752770460392
2025-07-18 00:41:00,873 - INFO - [test_connection] - 正在加载市场数据...
2025-07-18 00:41:04,954 - INFO - [test_connection] - 市场数据加载成功
2025-07-18 00:41:04,954 - INFO - [test_connection] - 交易对 SOL/USDT:USDT 验证成功
2025-07-18 00:41:04,954 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-18 00:41:06,220 - INFO - [test_connection] - Ticker 获取成功: SOL/USDT:USDT @ 171.73
2025-07-18 00:41:06,220 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-18 00:41:07,483 - INFO - [test_connection] - 余额获取成功，USDT余额: 1656.93173557
2025-07-18 00:41:07,483 - INFO - [test_connection] - API连接测试完成！
2025-07-18 00:41:07,483 - INFO - [setup] - 正在加载市场数据...
2025-07-18 00:41:11,593 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-18 00:41:12,851 - INFO - [set_hedge_mode] - 当前已是双向持仓模式，无需更改
2025-07-18 00:41:14,125 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-18 00:41:18,988 - INFO - [full_state_sync] - 同步到 0 个有效挂单
2025-07-18 00:41:22,725 - INFO - [fetch_listen_key] - 成功获取新的 listen key: QtHdX16DhU...
2025-07-18 00:41:22,725 - INFO - [setup] - 初始化设置完成。
2025-07-18 00:41:22,725 - INFO - [setup] - 当前持仓: 多头=0.0, 空头=0.0
2025-07-18 00:41:26,395 - INFO - [full_state_sync] - 同步到 0 个有效挂单
2025-07-18 00:41:26,396 - INFO - [cancel_all_open_orders] - 正在尝试取消所有挂单...
2025-07-18 00:41:27,662 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-18 00:41:27,663 - INFO - [run] - 所有现有挂单已取消，准备启动策略...
2025-07-18 00:41:27,663 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/QtHdX16DhUqt5aA7jIhGigXLdeKcwDw9IErkpbdQiOQ6uNQyVJgGJUHcLUV5LFQ6
2025-07-18 00:41:27,862 - INFO - [run] - WebSocket 连接成功。
2025-07-18 00:41:27,863 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdt@bookTicker'], 'id': 1}
2025-07-18 00:41:31,513 - INFO - [full_state_sync] - 同步到 0 个有效挂单
2025-07-18 00:41:31,515 - INFO - [execute_argm_strategy] - 网格重绘触发！新中枢: 167.1786, 新范围: [161.0211, 173.3361]
2025-07-18 00:41:31,515 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:41:31,516 - INFO - [rebalance_grid_orders] - 准备创建 23 个新订单
2025-07-18 00:41:33,540 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:41:33,541 - INFO - [rebalance_grid_orders] - 准备创建 23 个新订单
2025-07-18 00:41:35,557 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:41:35,557 - INFO - [rebalance_grid_orders] - 准备创建 23 个新订单
2025-07-18 00:41:37,571 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:41:37,571 - INFO - [rebalance_grid_orders] - 准备创建 23 个新订单
2025-07-18 00:41:39,599 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:41:39,600 - INFO - [rebalance_grid_orders] - 准备创建 22 个新订单
2025-07-18 00:41:41,608 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:41:41,608 - INFO - [rebalance_grid_orders] - 准备创建 23 个新订单
2025-07-18 00:41:43,678 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:41:43,679 - INFO - [rebalance_grid_orders] - 准备创建 23 个新订单
2025-07-18 00:41:45,699 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:41:45,699 - INFO - [rebalance_grid_orders] - 准备创建 24 个新订单
2025-07-18 00:41:47,750 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:41:47,750 - INFO - [rebalance_grid_orders] - 准备创建 24 个新订单
2025-07-18 00:41:49,773 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:41:49,774 - INFO - [rebalance_grid_orders] - 准备创建 24 个新订单
2025-07-18 00:41:51,815 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:41:51,816 - INFO - [rebalance_grid_orders] - 准备创建 24 个新订单
2025-07-18 00:41:53,884 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:41:53,885 - INFO - [rebalance_grid_orders] - 准备创建 24 个新订单
2025-07-18 00:41:55,901 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:41:55,902 - INFO - [rebalance_grid_orders] - 准备创建 24 个新订单
2025-07-18 00:41:57,942 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:41:57,942 - INFO - [rebalance_grid_orders] - 准备创建 24 个新订单
2025-07-18 00:41:59,964 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:41:59,964 - INFO - [rebalance_grid_orders] - 准备创建 24 个新订单
2025-07-18 00:42:01,977 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:42:01,978 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:42:03,992 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:42:03,992 - INFO - [rebalance_grid_orders] - 准备创建 24 个新订单
2025-07-18 00:42:06,023 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:42:06,023 - INFO - [rebalance_grid_orders] - 准备创建 24 个新订单
2025-07-18 00:42:08,036 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:42:08,037 - INFO - [rebalance_grid_orders] - 准备创建 24 个新订单
2025-07-18 00:42:10,070 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:42:10,071 - INFO - [rebalance_grid_orders] - 准备创建 24 个新订单
2025-07-18 00:42:12,080 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:42:12,080 - INFO - [rebalance_grid_orders] - 准备创建 24 个新订单
2025-07-18 00:42:14,092 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:42:14,092 - INFO - [rebalance_grid_orders] - 准备创建 24 个新订单
2025-07-18 00:42:16,115 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:42:16,115 - INFO - [rebalance_grid_orders] - 准备创建 24 个新订单
2025-07-18 00:42:18,222 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:42:18,222 - INFO - [rebalance_grid_orders] - 准备创建 24 个新订单
2025-07-18 00:42:20,246 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:42:20,246 - INFO - [rebalance_grid_orders] - 准备创建 24 个新订单
2025-07-18 00:42:27,862 - INFO - [close] - 正在关闭程序...
2025-07-18 00:42:27,862 - INFO - [cancel_all_open_orders] - 正在尝试取消所有挂单...
2025-07-18 00:42:29,153 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-18 00:42:29,414 - INFO - [close] - 程序已关闭。
2025-07-18 00:48:22,255 - INFO - [main] - 启动前预检 API 连接...
2025-07-18 00:48:22,255 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-18 00:48:22,261 - INFO - [main] - 测试代理连接: http://httpbin.org/ip
2025-07-18 00:48:22,700 - INFO - [main] - 代理服务器连接正常 (测试URL: http://httpbin.org/ip)
2025-07-18 00:48:22,700 - INFO - [main] - 代理服务器测试通过
2025-07-18 00:48:22,705 - INFO - [main] - 正在测试 API 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/5
2025-07-18 00:48:22,787 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.08s
2025-07-18 00:48:22,788 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-18 00:48:22,788 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-07-18 00:48:22,796 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-18 00:48:22,796 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-18 00:48:22,798 - INFO - [setup] - 正在执行启动设置...
2025-07-18 00:48:22,798 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-18 00:48:22,799 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-18 00:48:22,804 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-18 00:48:22,893 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-18 00:48:22,894 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-18 00:48:23,296 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-18 00:48:23,297 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-18 00:48:23,618 - INFO - [test_connection] - 服务器时间获取成功: 1752770903138
2025-07-18 00:48:23,619 - INFO - [test_connection] - 正在加载市场数据...
2025-07-18 00:48:27,647 - INFO - [test_connection] - 市场数据加载成功
2025-07-18 00:48:27,647 - INFO - [test_connection] - 交易对 SOL/USDT:USDT 验证成功
2025-07-18 00:48:27,647 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-18 00:48:28,909 - INFO - [test_connection] - Ticker 获取成功: SOL/USDT:USDT @ 172.36
2025-07-18 00:48:28,909 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-18 00:48:30,180 - INFO - [test_connection] - 余额获取成功，USDT余额: 1656.93173557
2025-07-18 00:48:30,180 - INFO - [test_connection] - API连接测试完成！
2025-07-18 00:48:30,180 - INFO - [setup] - 正在加载市场数据...
2025-07-18 00:48:34,250 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-18 00:48:35,523 - INFO - [set_hedge_mode] - 当前已是双向持仓模式，无需更改
2025-07-18 00:48:36,773 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-18 00:48:41,641 - INFO - [full_state_sync] - 同步到 0 个有效挂单
2025-07-18 00:48:45,383 - INFO - [fetch_listen_key] - 成功获取新的 listen key: QtHdX16DhU...
2025-07-18 00:48:45,383 - INFO - [setup] - 初始化设置完成。
2025-07-18 00:48:45,383 - INFO - [setup] - 当前持仓: 多头=0.0, 空头=0.0
2025-07-18 00:48:49,040 - INFO - [full_state_sync] - 同步到 0 个有效挂单
2025-07-18 00:48:49,040 - INFO - [cancel_all_open_orders] - 正在尝试取消所有挂单...
2025-07-18 00:48:50,302 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-18 00:48:50,302 - INFO - [run] - 所有现有挂单已取消，准备启动策略...
2025-07-18 00:48:50,302 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/QtHdX16DhUqt5aA7jIhGigXLdeKcwDw9IErkpbdQiOQ6uNQyVJgGJUHcLUV5LFQ6
2025-07-18 00:48:50,497 - INFO - [run] - WebSocket 连接成功。
2025-07-18 00:48:50,497 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdt@bookTicker'], 'id': 1}
2025-07-18 00:48:54,158 - INFO - [full_state_sync] - 同步到 0 个有效挂单
2025-07-18 00:48:54,161 - INFO - [execute_argm_strategy] - 网格重绘触发！新中枢: 167.1796, 新范围: [161.0346, 173.3247]
2025-07-18 00:48:54,161 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:48:54,161 - INFO - [rebalance_grid_orders] - 准备创建 23 个新订单
2025-07-18 00:48:56,288 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:48:56,289 - INFO - [rebalance_grid_orders] - 准备创建 23 个新订单
2025-07-18 00:48:58,379 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:48:58,379 - INFO - [rebalance_grid_orders] - 准备创建 23 个新订单
2025-07-18 00:49:00,511 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:49:00,511 - INFO - [rebalance_grid_orders] - 准备创建 23 个新订单
2025-07-18 00:49:02,537 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:49:02,537 - INFO - [rebalance_grid_orders] - 准备创建 23 个新订单
2025-07-18 00:49:04,619 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:49:04,619 - INFO - [rebalance_grid_orders] - 准备创建 22 个新订单
2025-07-18 00:49:06,652 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:49:06,653 - INFO - [rebalance_grid_orders] - 准备创建 22 个新订单
2025-07-18 00:49:08,672 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:49:08,673 - INFO - [rebalance_grid_orders] - 准备创建 22 个新订单
2025-07-18 00:49:10,740 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:49:10,741 - INFO - [rebalance_grid_orders] - 准备创建 22 个新订单
2025-07-18 00:49:12,785 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:49:12,785 - INFO - [rebalance_grid_orders] - 准备创建 22 个新订单
2025-07-18 00:49:14,805 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:49:14,806 - INFO - [rebalance_grid_orders] - 准备创建 22 个新订单
2025-07-18 00:49:16,864 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:49:16,865 - INFO - [rebalance_grid_orders] - 准备创建 22 个新订单
2025-07-18 00:49:18,953 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:49:18,954 - INFO - [rebalance_grid_orders] - 准备创建 22 个新订单
2025-07-18 00:49:20,961 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:49:20,962 - INFO - [rebalance_grid_orders] - 准备创建 22 个新订单
2025-07-18 00:49:23,381 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:49:23,381 - INFO - [rebalance_grid_orders] - 准备创建 22 个新订单
2025-07-18 00:49:25,474 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:49:25,474 - INFO - [rebalance_grid_orders] - 准备创建 22 个新订单
2025-07-18 00:49:27,502 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:49:27,503 - INFO - [rebalance_grid_orders] - 准备创建 22 个新订单
2025-07-18 00:49:29,599 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:49:29,599 - INFO - [rebalance_grid_orders] - 准备创建 22 个新订单
2025-07-18 00:49:31,621 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:49:31,621 - INFO - [rebalance_grid_orders] - 准备创建 22 个新订单
2025-07-18 00:49:33,786 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:49:33,786 - INFO - [rebalance_grid_orders] - 准备创建 22 个新订单
2025-07-18 00:49:35,857 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:49:35,857 - INFO - [rebalance_grid_orders] - 准备创建 22 个新订单
2025-07-18 00:49:37,934 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:49:37,935 - INFO - [rebalance_grid_orders] - 准备创建 22 个新订单
2025-07-18 00:49:40,151 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:49:40,151 - INFO - [rebalance_grid_orders] - 准备创建 22 个新订单
2025-07-18 00:49:42,189 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:49:42,189 - INFO - [rebalance_grid_orders] - 准备创建 23 个新订单
2025-07-18 00:49:44,341 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:49:44,342 - INFO - [rebalance_grid_orders] - 准备创建 23 个新订单
2025-07-18 00:49:46,485 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:49:46,485 - INFO - [rebalance_grid_orders] - 准备创建 23 个新订单
2025-07-18 00:49:48,527 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:49:48,528 - INFO - [rebalance_grid_orders] - 准备创建 23 个新订单
2025-07-18 00:49:50,605 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:49:50,606 - INFO - [rebalance_grid_orders] - 准备创建 23 个新订单
2025-07-18 00:49:52,756 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:49:52,756 - INFO - [rebalance_grid_orders] - 准备创建 23 个新订单
2025-07-18 00:49:57,953 - INFO - [full_state_sync] - 同步到 0 个有效挂单
2025-07-18 00:49:59,231 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:49:59,231 - INFO - [rebalance_grid_orders] - 准备创建 23 个新订单
2025-07-18 00:50:01,303 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:50:01,304 - INFO - [rebalance_grid_orders] - 准备创建 23 个新订单
2025-07-18 00:50:03,358 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:50:03,359 - INFO - [rebalance_grid_orders] - 准备创建 23 个新订单
2025-07-18 00:50:05,436 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:50:05,436 - INFO - [rebalance_grid_orders] - 准备创建 23 个新订单
2025-07-18 00:50:07,487 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:50:07,487 - INFO - [rebalance_grid_orders] - 准备创建 23 个新订单
2025-07-18 00:50:09,586 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:50:09,587 - INFO - [rebalance_grid_orders] - 准备创建 23 个新订单
2025-07-18 00:50:11,651 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:50:11,652 - INFO - [rebalance_grid_orders] - 准备创建 24 个新订单
2025-07-18 00:50:13,774 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:50:13,774 - INFO - [rebalance_grid_orders] - 准备创建 24 个新订单
2025-07-18 00:50:15,868 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:50:15,868 - INFO - [rebalance_grid_orders] - 准备创建 24 个新订单
2025-07-18 00:50:17,928 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:50:17,929 - INFO - [rebalance_grid_orders] - 准备创建 24 个新订单
2025-07-18 00:50:19,943 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:50:19,943 - INFO - [rebalance_grid_orders] - 准备创建 24 个新订单
2025-07-18 00:50:21,971 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:50:21,971 - INFO - [rebalance_grid_orders] - 准备创建 24 个新订单
2025-07-18 00:50:24,020 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:50:24,020 - INFO - [rebalance_grid_orders] - 准备创建 24 个新订单
2025-07-18 00:50:26,041 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:50:26,041 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:50:28,084 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:50:28,085 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:50:30,145 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:50:30,146 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:50:32,214 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:50:32,214 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:50:34,265 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:50:34,265 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:50:36,317 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:50:36,318 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:50:38,348 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:50:38,348 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:50:40,366 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:50:40,366 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:50:42,389 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:50:42,389 - INFO - [rebalance_grid_orders] - 准备创建 24 个新订单
2025-07-18 00:50:44,464 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:50:44,464 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:50:46,684 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:50:46,684 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:50:48,734 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:50:48,734 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:50:50,756 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:50:50,757 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:50:52,801 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:50:52,801 - INFO - [rebalance_grid_orders] - 准备创建 24 个新订单
2025-07-18 00:50:54,909 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:50:54,911 - INFO - [rebalance_grid_orders] - 准备创建 24 个新订单
2025-07-18 00:50:56,980 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:50:56,981 - INFO - [rebalance_grid_orders] - 准备创建 24 个新订单
2025-07-18 00:51:02,893 - INFO - [full_state_sync] - 同步到 0 个有效挂单
2025-07-18 00:51:04,160 - ERROR - [update_price_series] - 更新K线数据失败 (尝试 1/3): K线数据不足: 需要200条，获得178条
2025-07-18 00:51:07,636 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:51:07,637 - INFO - [rebalance_grid_orders] - 准备创建 24 个新订单
2025-07-18 00:51:09,871 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:51:09,871 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:51:11,917 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:51:11,917 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:51:13,971 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:51:13,971 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:51:16,079 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:51:16,079 - INFO - [rebalance_grid_orders] - 准备创建 24 个新订单
2025-07-18 00:51:18,313 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:51:18,313 - INFO - [rebalance_grid_orders] - 准备创建 24 个新订单
2025-07-18 00:51:20,432 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:51:20,433 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:51:22,627 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:51:22,627 - INFO - [rebalance_grid_orders] - 准备创建 25 个新订单
2025-07-18 00:51:24,680 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-18 00:51:24,680 - INFO - [rebalance_grid_orders] - 准备创建 24 个新订单
2025-07-18 00:51:31,310 - INFO - [close] - 正在关闭程序...
2025-07-18 00:51:31,310 - INFO - [cancel_all_open_orders] - 正在尝试取消所有挂单...
2025-07-18 00:51:33,788 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-18 00:51:34,046 - INFO - [close] - 程序已关闭。
2025-07-18 01:08:45,635 - INFO - [main] - 启动前预检 API 连接...
2025-07-18 01:08:45,635 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-18 01:08:45,642 - INFO - [main] - 测试代理连接: http://httpbin.org/ip
2025-07-18 01:08:49,193 - INFO - [main] - 代理服务器连接正常 (测试URL: http://httpbin.org/ip)
2025-07-18 01:08:49,193 - INFO - [main] - 代理服务器测试通过
2025-07-18 01:08:49,199 - INFO - [main] - 正在测试 API 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/5
2025-07-18 01:08:49,284 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.09s
2025-07-18 01:08:49,285 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-18 01:08:49,285 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-07-18 01:08:49,292 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-18 01:08:49,292 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-18 01:08:49,294 - INFO - [setup] - 正在执行启动设置...
2025-07-18 01:08:49,294 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-18 01:08:49,294 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-18 01:08:49,299 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-18 01:08:49,389 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-18 01:08:49,389 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-18 01:08:49,791 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-18 01:08:49,792 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-18 01:08:50,128 - INFO - [test_connection] - 服务器时间获取成功: 1752772129647
2025-07-18 01:08:50,128 - INFO - [test_connection] - 正在加载市场数据...
2025-07-18 01:08:54,197 - INFO - [test_connection] - 市场数据加载成功
2025-07-18 01:08:54,197 - INFO - [test_connection] - 交易对 SOL/USDT:USDT 验证成功
2025-07-18 01:08:54,197 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-18 01:08:55,452 - INFO - [test_connection] - Ticker 获取成功: SOL/USDT:USDT @ 173.09
2025-07-18 01:08:55,452 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-18 01:08:56,721 - INFO - [test_connection] - 余额获取成功，USDT余额: 1656.93173557
2025-07-18 01:08:56,721 - INFO - [test_connection] - API连接测试完成！
2025-07-18 01:08:56,721 - INFO - [setup] - 正在加载市场数据...
2025-07-18 01:09:00,840 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-18 01:09:02,111 - INFO - [set_hedge_mode] - 当前已是双向持仓模式，无需更改
2025-07-18 01:09:03,376 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-18 01:09:08,246 - INFO - [full_state_sync] - 同步到 0 个有效挂单
2025-07-18 01:09:11,971 - INFO - [fetch_listen_key] - 成功获取新的 listen key: QtHdX16DhU...
2025-07-18 01:09:11,972 - INFO - [setup] - 初始化设置完成。
2025-07-18 01:09:11,972 - INFO - [setup] - 当前持仓: 多头=0.0, 空头=0.0
2025-07-18 01:09:15,643 - INFO - [full_state_sync] - 同步到 0 个有效挂单
2025-07-18 01:09:15,643 - INFO - [cancel_all_open_orders] - 正在尝试取消所有挂单...
2025-07-18 01:09:16,898 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-18 01:09:16,898 - INFO - [run] - 所有现有挂单已取消，准备启动策略...
2025-07-18 01:09:16,898 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/QtHdX16DhUqt5aA7jIhGigXLdeKcwDw9IErkpbdQiOQ6uNQyVJgGJUHcLUV5LFQ6
2025-07-18 01:09:17,098 - INFO - [run] - WebSocket 连接成功。
2025-07-18 01:09:17,099 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdt@bookTicker'], 'id': 1}
2025-07-18 01:09:20,762 - INFO - [full_state_sync] - 同步到 0 个有效挂单
2025-07-18 01:09:20,764 - INFO - [execute_argm_strategy] - 网格重绘触发！新中枢: 167.3073, 新范围: [161.3890, 173.2256]
2025-07-18 01:14:37,801 - INFO - [main] - 启动前预检 API 连接...
2025-07-18 01:14:37,802 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-18 01:14:37,808 - INFO - [main] - 测试代理连接: http://httpbin.org/ip
2025-07-18 01:14:40,033 - INFO - [main] - 代理服务器连接正常 (测试URL: http://httpbin.org/ip)
2025-07-18 01:14:40,034 - INFO - [main] - 代理服务器测试通过
2025-07-18 01:14:40,039 - INFO - [main] - 正在测试 API 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/5
2025-07-18 01:14:40,122 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.08s
2025-07-18 01:14:40,123 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-18 01:14:40,123 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-07-18 01:14:40,130 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-18 01:14:40,131 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-18 01:14:40,132 - INFO - [setup] - 正在执行启动设置...
2025-07-18 01:14:40,132 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-18 01:14:40,132 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-18 01:14:40,137 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-18 01:14:40,216 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-18 01:14:40,216 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-18 01:14:40,599 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-18 01:14:40,599 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-18 01:14:40,935 - INFO - [test_connection] - 服务器时间获取成功: 1752772480455
2025-07-18 01:14:40,937 - INFO - [test_connection] - 正在加载市场数据...
2025-07-18 01:14:44,986 - INFO - [test_connection] - 市场数据加载成功
2025-07-18 01:14:44,987 - INFO - [test_connection] - 交易对 SOL/USDT:USDT 验证成功
2025-07-18 01:14:44,987 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-18 01:14:46,246 - INFO - [test_connection] - Ticker 获取成功: SOL/USDT:USDT @ 173.08
2025-07-18 01:14:46,246 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-18 01:14:47,523 - INFO - [test_connection] - 余额获取成功，USDT余额: 1656.93173557
2025-07-18 01:14:47,524 - INFO - [test_connection] - API连接测试完成！
2025-07-18 01:14:47,524 - INFO - [setup] - 正在加载市场数据...
2025-07-18 01:14:51,595 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-18 01:14:52,873 - INFO - [set_hedge_mode] - 当前已是双向持仓模式，无需更改
2025-07-18 01:14:54,122 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-18 01:14:58,992 - INFO - [full_state_sync] - 同步到 0 个有效挂单
2025-07-18 01:15:02,722 - INFO - [fetch_listen_key] - 成功获取新的 listen key: QtHdX16DhU...
2025-07-18 01:15:02,723 - INFO - [setup] - 初始化设置完成。
2025-07-18 01:15:02,723 - INFO - [setup] - 当前持仓: 多头=0.0, 空头=0.0
2025-07-18 01:15:06,394 - INFO - [full_state_sync] - 同步到 0 个有效挂单
2025-07-18 01:15:06,394 - INFO - [cancel_all_open_orders] - 正在尝试取消所有挂单...
2025-07-18 01:15:07,661 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-18 01:15:07,662 - INFO - [run] - 所有现有挂单已取消，准备启动策略...
2025-07-18 01:15:07,662 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/QtHdX16DhUqt5aA7jIhGigXLdeKcwDw9IErkpbdQiOQ6uNQyVJgGJUHcLUV5LFQ6
2025-07-18 01:15:07,872 - INFO - [run] - WebSocket 连接成功。
2025-07-18 01:15:07,872 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdt@bookTicker'], 'id': 1}
2025-07-18 01:15:11,621 - INFO - [full_state_sync] - 同步到 0 个有效挂单
2025-07-18 01:15:11,624 - INFO - [execute_argm_strategy] - 网格重绘触发！新中枢: 167.3090, 新范围: [161.3510, 173.2670]
2025-07-18 01:18:11,929 - INFO - [main] - 启动前预检 API 连接...
2025-07-18 01:18:11,929 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-18 01:18:11,938 - INFO - [main] - 测试代理连接: http://httpbin.org/ip
2025-07-18 01:18:14,625 - INFO - [main] - 代理服务器连接正常 (测试URL: http://httpbin.org/ip)
2025-07-18 01:18:14,626 - INFO - [main] - 代理服务器测试通过
2025-07-18 01:18:14,631 - INFO - [main] - 正在测试 API 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/5
2025-07-18 01:18:14,717 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.09s
2025-07-18 01:18:14,718 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-18 01:18:14,718 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-07-18 01:18:14,725 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-18 01:18:14,726 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-18 01:18:14,727 - INFO - [setup] - 正在执行启动设置...
2025-07-18 01:18:14,727 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-18 01:18:14,727 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-18 01:18:14,733 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-18 01:18:14,814 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-18 01:18:14,814 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-18 01:18:15,211 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-18 01:18:15,212 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-18 01:18:15,536 - INFO - [test_connection] - 服务器时间获取成功: 1752772695055
2025-07-18 01:18:15,536 - INFO - [test_connection] - 正在加载市场数据...
2025-07-18 01:18:19,573 - INFO - [test_connection] - 市场数据加载成功
2025-07-18 01:18:19,573 - INFO - [test_connection] - 交易对 SOL/USDT:USDT 验证成功
2025-07-18 01:18:19,573 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-18 01:18:20,842 - INFO - [test_connection] - Ticker 获取成功: SOL/USDT:USDT @ 172.94
2025-07-18 01:18:20,842 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-18 01:18:22,100 - INFO - [test_connection] - 余额获取成功，USDT余额: 1656.93173557
2025-07-18 01:18:22,101 - INFO - [test_connection] - API连接测试完成！
2025-07-18 01:18:22,101 - INFO - [setup] - 正在加载市场数据...
2025-07-18 01:18:26,179 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-18 01:18:27,440 - INFO - [set_hedge_mode] - 当前已是双向持仓模式，无需更改
2025-07-18 01:18:28,705 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-18 01:18:33,564 - INFO - [full_state_sync] - 同步到 0 个有效挂单
2025-07-18 01:18:37,297 - INFO - [fetch_listen_key] - 成功获取新的 listen key: QtHdX16DhU...
2025-07-18 01:18:37,298 - INFO - [setup] - 初始化设置完成。
2025-07-18 01:18:37,298 - INFO - [setup] - 当前持仓: 多头=0.0, 空头=0.0
2025-07-18 01:18:40,964 - INFO - [full_state_sync] - 同步到 0 个有效挂单
2025-07-18 01:18:40,964 - INFO - [cancel_all_open_orders] - 正在尝试取消所有挂单...
2025-07-18 01:18:42,231 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-18 01:18:42,231 - INFO - [run] - 所有现有挂单已取消，准备启动策略...
2025-07-18 01:18:42,231 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/QtHdX16DhUqt5aA7jIhGigXLdeKcwDw9IErkpbdQiOQ6uNQyVJgGJUHcLUV5LFQ6
2025-07-18 01:18:42,429 - INFO - [run] - WebSocket 连接成功。
2025-07-18 01:18:42,430 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdt@bookTicker'], 'id': 1}
2025-07-18 01:18:46,078 - INFO - [full_state_sync] - 同步到 0 个有效挂单
2025-07-18 01:18:46,081 - INFO - [execute_argm_strategy] - 网格重绘触发！新中枢: 167.3053, 新范围: [161.3918, 173.2188]
2025-07-18 01:21:17,061 - INFO - [main] - 启动前预检 API 连接...
2025-07-18 01:21:17,061 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-18 01:21:17,068 - INFO - [main] - 测试代理连接: http://httpbin.org/ip
2025-07-18 01:21:19,461 - INFO - [main] - 代理服务器连接正常 (测试URL: http://httpbin.org/ip)
2025-07-18 01:21:19,461 - INFO - [main] - 代理服务器测试通过
2025-07-18 01:21:19,480 - INFO - [main] - 正在测试 API 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/5
2025-07-18 01:21:19,574 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.09s
2025-07-18 01:21:19,576 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-18 01:21:19,576 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-07-18 01:21:19,583 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-18 01:21:19,583 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-18 01:21:19,585 - INFO - [setup] - 正在执行启动设置...
2025-07-18 01:21:19,585 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-18 01:21:19,585 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-18 01:21:19,590 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-18 01:21:19,672 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-18 01:21:19,672 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-18 01:21:20,094 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-18 01:21:20,094 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-18 01:21:20,428 - INFO - [test_connection] - 服务器时间获取成功: 1752772879946
2025-07-18 01:21:20,428 - INFO - [test_connection] - 正在加载市场数据...
2025-07-18 01:21:24,597 - INFO - [test_connection] - 市场数据加载成功
2025-07-18 01:21:24,597 - INFO - [test_connection] - 交易对 SOL/USDT:USDT 验证成功
2025-07-18 01:21:24,597 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-18 01:21:25,867 - INFO - [test_connection] - Ticker 获取成功: SOL/USDT:USDT @ 172.74
2025-07-18 01:21:25,867 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-18 01:21:27,133 - INFO - [test_connection] - 余额获取成功，USDT余额: 1656.93173557
2025-07-18 01:21:27,133 - INFO - [test_connection] - API连接测试完成！
2025-07-18 01:21:27,133 - INFO - [setup] - 正在加载市场数据...
2025-07-18 01:21:31,216 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-18 01:21:32,470 - INFO - [set_hedge_mode] - 当前已是双向持仓模式，无需更改
2025-07-18 01:21:33,739 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-18 01:21:38,605 - INFO - [full_state_sync] - 同步到 0 个有效挂单
2025-07-18 01:21:39,875 - ERROR - [update_price_series] - 更新K线数据失败 (尝试 1/3): K线数据不足: 需要200条，获得179条
2025-07-18 01:21:45,807 - INFO - [fetch_listen_key] - 成功获取新的 listen key: QtHdX16DhU...
2025-07-18 01:21:45,808 - INFO - [setup] - 初始化设置完成。
2025-07-18 01:21:45,808 - INFO - [setup] - 当前持仓: 多头=0.0, 空头=0.0
2025-07-18 01:21:49,461 - INFO - [full_state_sync] - 同步到 0 个有效挂单
2025-07-18 01:21:49,461 - INFO - [cancel_all_open_orders] - 正在尝试取消所有挂单...
2025-07-18 01:21:50,729 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-18 01:21:50,730 - INFO - [run] - 所有现有挂单已取消，准备启动策略...
2025-07-18 01:21:50,730 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/QtHdX16DhUqt5aA7jIhGigXLdeKcwDw9IErkpbdQiOQ6uNQyVJgGJUHcLUV5LFQ6
2025-07-18 01:21:50,927 - INFO - [run] - WebSocket 连接成功。
2025-07-18 01:21:50,927 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdt@bookTicker'], 'id': 1}
2025-07-18 01:21:54,583 - INFO - [full_state_sync] - 同步到 0 个有效挂单
2025-07-18 01:21:54,586 - INFO - [execute_argm_strategy] - 网格重绘触发！新中枢: 167.3010, 新范围: [161.4418, 173.1601]
2025-07-18 01:24:00,959 - INFO - [main] - 启动前预检 API 连接...
2025-07-18 01:24:00,959 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-18 01:24:00,965 - INFO - [main] - 测试代理连接: http://httpbin.org/ip
2025-07-18 01:24:04,155 - INFO - [main] - 代理服务器连接正常 (测试URL: http://httpbin.org/ip)
2025-07-18 01:24:04,156 - INFO - [main] - 代理服务器测试通过
2025-07-18 01:24:04,161 - INFO - [main] - 正在测试 API 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/5
2025-07-18 01:24:04,247 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.09s
2025-07-18 01:24:04,248 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-18 01:24:04,248 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-07-18 01:24:04,255 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-18 01:24:04,255 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-18 01:24:04,256 - INFO - [setup] - 正在执行启动设置...
2025-07-18 01:24:04,256 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-18 01:24:04,256 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-18 01:24:04,262 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-18 01:24:04,339 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-18 01:24:04,340 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-18 01:24:04,766 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-18 01:24:04,767 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-18 01:24:05,090 - INFO - [test_connection] - 服务器时间获取成功: 1752773044610
2025-07-18 01:24:05,091 - INFO - [test_connection] - 正在加载市场数据...
2025-07-18 01:24:09,140 - INFO - [test_connection] - 市场数据加载成功
2025-07-18 01:24:09,141 - INFO - [test_connection] - 交易对 SOL/USDT:USDT 验证成功
2025-07-18 01:24:09,141 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-18 01:24:10,396 - INFO - [test_connection] - Ticker 获取成功: SOL/USDT:USDT @ 172.83
2025-07-18 01:24:10,396 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-18 01:24:11,661 - INFO - [test_connection] - 余额获取成功，USDT余额: 1656.93173557
2025-07-18 01:24:11,661 - INFO - [test_connection] - API连接测试完成！
2025-07-18 01:24:11,661 - INFO - [setup] - 正在加载市场数据...
2025-07-18 01:24:15,753 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-18 01:24:17,020 - INFO - [set_hedge_mode] - 当前已是双向持仓模式，无需更改
2025-07-18 01:24:18,283 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-18 01:24:23,144 - INFO - [full_state_sync] - 同步到 0 个有效挂单
2025-07-18 01:24:26,872 - INFO - [fetch_listen_key] - 成功获取新的 listen key: QtHdX16DhU...
2025-07-18 01:24:26,873 - INFO - [setup] - 初始化设置完成。
2025-07-18 01:24:26,873 - INFO - [setup] - 当前持仓: 多头=0.0, 空头=0.0
2025-07-18 01:24:30,542 - INFO - [full_state_sync] - 同步到 0 个有效挂单
2025-07-18 01:24:30,542 - INFO - [cancel_all_open_orders] - 正在尝试取消所有挂单...
2025-07-18 01:24:31,900 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-18 01:24:31,900 - INFO - [run] - 所有现有挂单已取消，准备启动策略...
2025-07-18 01:24:31,900 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/QtHdX16DhUqt5aA7jIhGigXLdeKcwDw9IErkpbdQiOQ6uNQyVJgGJUHcLUV5LFQ6
2025-07-18 01:24:32,099 - INFO - [run] - WebSocket 连接成功。
2025-07-18 01:24:32,100 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdt@bookTicker'], 'id': 1}
2025-07-18 01:24:35,758 - INFO - [full_state_sync] - 同步到 0 个有效挂单
2025-07-18 01:24:35,761 - INFO - [execute_argm_strategy] - 网格重绘触发！新中枢: 167.3018, 新范围: [161.4196, 173.1840]
2025-07-18 01:27:12,800 - INFO - [main] - 启动前预检 API 连接...
2025-07-18 01:27:12,800 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-18 01:27:12,807 - INFO - [main] - 测试代理连接: http://httpbin.org/ip
2025-07-18 01:27:20,168 - INFO - [main] - 代理服务器连接正常 (测试URL: http://httpbin.org/ip)
2025-07-18 01:27:20,168 - INFO - [main] - 代理服务器测试通过
2025-07-18 01:27:20,173 - INFO - [main] - 正在测试 API 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/5
2025-07-18 01:27:20,257 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.08s
2025-07-18 01:27:20,258 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-18 01:27:20,258 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-07-18 01:27:20,265 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-18 01:27:20,266 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-18 01:27:20,267 - INFO - [setup] - 正在执行启动设置...
2025-07-18 01:27:20,268 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-18 01:27:20,268 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-18 01:27:20,273 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-18 01:27:20,359 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-18 01:27:20,360 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-18 01:27:20,755 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-18 01:27:20,756 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-18 01:27:21,089 - INFO - [test_connection] - 服务器时间获取成功: 1752773240610
2025-07-18 01:27:21,090 - INFO - [test_connection] - 正在加载市场数据...
2025-07-18 01:27:25,255 - INFO - [test_connection] - 市场数据加载成功
2025-07-18 01:27:25,256 - INFO - [test_connection] - 交易对 SOL/USDT:USDT 验证成功
2025-07-18 01:27:25,256 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-18 01:27:26,529 - INFO - [test_connection] - Ticker 获取成功: SOL/USDT:USDT @ 172.85
2025-07-18 01:27:26,529 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-18 01:27:27,797 - INFO - [test_connection] - 余额获取成功，USDT余额: 1656.93173557
2025-07-18 01:27:27,797 - INFO - [test_connection] - API连接测试完成！
2025-07-18 01:27:27,797 - INFO - [setup] - 正在加载市场数据...
2025-07-18 01:27:31,872 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-18 01:27:33,147 - INFO - [set_hedge_mode] - 当前已是双向持仓模式，无需更改
2025-07-18 01:27:34,413 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-18 01:27:39,276 - INFO - [full_state_sync] - 同步到 0 个有效挂单
2025-07-18 01:27:40,532 - ERROR - [update_price_series] - 更新K线数据失败 (尝试 1/3): K线数据不足: 需要200条，获得179条
2025-07-18 01:27:46,468 - INFO - [fetch_listen_key] - 成功获取新的 listen key: QtHdX16DhU...
2025-07-18 01:27:46,468 - INFO - [setup] - 初始化设置完成。
2025-07-18 01:27:46,468 - INFO - [setup] - 当前持仓: 多头=0.0, 空头=0.0
2025-07-18 01:27:50,135 - INFO - [full_state_sync] - 同步到 0 个有效挂单
2025-07-18 01:27:50,135 - INFO - [cancel_all_open_orders] - 正在尝试取消所有挂单...
2025-07-18 01:27:51,388 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-18 01:27:51,388 - INFO - [run] - 所有现有挂单已取消，准备启动策略...
2025-07-18 01:27:51,388 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/QtHdX16DhUqt5aA7jIhGigXLdeKcwDw9IErkpbdQiOQ6uNQyVJgGJUHcLUV5LFQ6
2025-07-18 01:27:51,591 - INFO - [run] - WebSocket 连接成功。
2025-07-18 01:27:51,591 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdt@bookTicker'], 'id': 1}
2025-07-18 01:27:55,266 - INFO - [full_state_sync] - 同步到 0 个有效挂单
2025-07-18 01:27:55,268 - INFO - [execute_argm_strategy] - 网格重绘触发！新中枢: 167.3032, 新范围: [161.4026, 173.2039]
2025-07-18 01:27:55,269 - INFO - [rebalance_grid_orders] - === rebalance_grid_orders函数开始执行 ===
2025-07-18 01:31:14,039 - INFO - [main] - 启动前预检 API 连接...
2025-07-18 01:31:14,039 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-18 01:31:14,047 - INFO - [main] - 测试代理连接: http://httpbin.org/ip
2025-07-18 01:31:15,864 - INFO - [main] - 代理服务器连接正常 (测试URL: http://httpbin.org/ip)
2025-07-18 01:31:15,864 - INFO - [main] - 代理服务器测试通过
2025-07-18 01:31:15,871 - INFO - [main] - 正在测试 API 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/5
2025-07-18 01:31:15,951 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.08s
2025-07-18 01:31:15,951 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-18 01:31:15,952 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-07-18 01:31:15,958 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-18 01:31:15,958 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-18 01:31:15,960 - INFO - [setup] - 正在执行启动设置...
2025-07-18 01:31:15,960 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-18 01:31:15,960 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-18 01:31:15,968 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-18 01:31:16,056 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-18 01:31:16,056 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-18 01:31:16,477 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-18 01:31:16,477 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-18 01:31:16,808 - INFO - [test_connection] - 服务器时间获取成功: 1752773476325
2025-07-18 01:31:16,808 - INFO - [test_connection] - 正在加载市场数据...
2025-07-18 01:31:20,830 - INFO - [test_connection] - 市场数据加载成功
2025-07-18 01:31:20,830 - INFO - [test_connection] - 交易对 SOL/USDT:USDT 验证成功
2025-07-18 01:31:20,830 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-18 01:31:22,103 - INFO - [test_connection] - Ticker 获取成功: SOL/USDT:USDT @ 172.99
2025-07-18 01:31:22,103 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-18 01:31:23,363 - INFO - [test_connection] - 余额获取成功，USDT余额: 1656.93173557
2025-07-18 01:31:23,363 - INFO - [test_connection] - API连接测试完成！
2025-07-18 01:31:23,363 - INFO - [setup] - 正在加载市场数据...
2025-07-18 01:31:27,463 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-18 01:31:28,731 - INFO - [set_hedge_mode] - 当前已是双向持仓模式，无需更改
2025-07-18 01:31:29,980 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-18 01:31:34,841 - INFO - [_internal_state_sync] - 同步到 0 个有效挂单
2025-07-18 01:31:38,586 - INFO - [fetch_listen_key] - 成功获取新的 listen key: QtHdX16DhU...
2025-07-18 01:31:38,587 - INFO - [setup] - 初始化设置完成。
2025-07-18 01:31:38,587 - INFO - [setup] - 当前持仓: 多头=0.0, 空头=0.0
2025-07-18 01:31:42,245 - INFO - [_internal_state_sync] - 同步到 0 个有效挂单
2025-07-18 01:31:42,245 - INFO - [cancel_all_open_orders] - 正在尝试取消所有挂单...
2025-07-18 01:31:43,503 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-18 01:31:43,503 - INFO - [run] - 所有现有挂单已取消，准备启动策略...
2025-07-18 01:31:43,503 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/QtHdX16DhUqt5aA7jIhGigXLdeKcwDw9IErkpbdQiOQ6uNQyVJgGJUHcLUV5LFQ6
2025-07-18 01:31:43,700 - INFO - [run] - WebSocket 连接成功。
2025-07-18 01:31:43,701 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdt@bookTicker'], 'id': 1}
2025-07-18 01:31:47,368 - INFO - [_internal_state_sync] - 同步到 0 个有效挂单
2025-07-18 01:31:47,370 - INFO - [execute_argm_strategy] - 网格重绘触发！新中枢: 167.3032, 新范围: [161.4026, 173.2039]
2025-07-18 01:31:47,371 - INFO - [rebalance_grid_orders] - === rebalance_grid_orders函数开始执行 ===
2025-07-18 01:34:57,784 - INFO - [main] - 启动前预检 API 连接...
2025-07-18 01:34:57,785 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-18 01:34:57,791 - INFO - [main] - 测试代理连接: http://httpbin.org/ip
2025-07-18 01:34:58,885 - INFO - [main] - 代理服务器连接正常 (测试URL: http://httpbin.org/ip)
2025-07-18 01:34:58,885 - INFO - [main] - 代理服务器测试通过
2025-07-18 01:34:58,891 - INFO - [main] - 正在测试 API 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/5
2025-07-18 01:34:58,976 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.09s
2025-07-18 01:34:58,977 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-18 01:34:58,978 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-07-18 01:34:58,986 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-18 01:34:58,987 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-18 01:34:58,989 - INFO - [setup] - 正在执行启动设置...
2025-07-18 01:34:58,989 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-18 01:34:58,989 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-18 01:34:58,995 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-18 01:34:59,079 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-18 01:34:59,081 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-18 01:34:59,539 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-18 01:34:59,540 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-18 01:34:59,862 - INFO - [test_connection] - 服务器时间获取成功: 1752773699381
2025-07-18 01:34:59,862 - INFO - [test_connection] - 正在加载市场数据...
2025-07-18 01:35:03,900 - INFO - [test_connection] - 市场数据加载成功
2025-07-18 01:35:03,900 - INFO - [test_connection] - 交易对 SOL/USDT:USDT 验证成功
2025-07-18 01:35:03,900 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-18 01:35:05,164 - INFO - [test_connection] - Ticker 获取成功: SOL/USDT:USDT @ 172.97
2025-07-18 01:35:05,164 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-18 01:35:06,436 - INFO - [test_connection] - 余额获取成功，USDT余额: 1656.93173557
2025-07-18 01:35:06,436 - INFO - [test_connection] - API连接测试完成！
2025-07-18 01:35:06,436 - INFO - [setup] - 正在加载市场数据...
2025-07-18 01:35:10,526 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-18 01:35:11,792 - INFO - [set_hedge_mode] - 当前已是双向持仓模式，无需更改
2025-07-18 01:35:13,070 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-18 01:35:17,928 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 173.08
2025-07-18 01:35:17,928 - INFO - [_internal_state_sync] - 同步到 0 个有效挂单
2025-07-18 01:35:21,666 - INFO - [fetch_listen_key] - 成功获取新的 listen key: QtHdX16DhU...
2025-07-18 01:35:21,667 - INFO - [setup] - 初始化设置完成。
2025-07-18 01:35:21,667 - INFO - [setup] - 当前持仓: 多头=0.0, 空头=0.0
2025-07-18 01:35:25,321 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 173.15
2025-07-18 01:35:25,321 - INFO - [_internal_state_sync] - 同步到 0 个有效挂单
2025-07-18 01:35:25,321 - INFO - [cancel_all_open_orders] - 正在尝试取消所有挂单...
2025-07-18 01:35:26,585 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-18 01:35:26,585 - INFO - [run] - 所有现有挂单已取消，准备启动策略...
2025-07-18 01:35:26,585 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/QtHdX16DhUqt5aA7jIhGigXLdeKcwDw9IErkpbdQiOQ6uNQyVJgGJUHcLUV5LFQ6
2025-07-18 01:35:26,787 - INFO - [run] - WebSocket 连接成功。
2025-07-18 01:35:26,788 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdt@bookTicker'], 'id': 1}
2025-07-18 01:35:30,449 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 173.15
2025-07-18 01:35:30,449 - INFO - [_internal_state_sync] - 同步到 0 个有效挂单
2025-07-18 01:35:30,451 - INFO - [execute_argm_strategy] - 网格重绘触发！新中枢: 167.3088, 新范围: [161.3404, 173.2772]
2025-07-18 01:35:30,452 - INFO - [rebalance_grid_orders] - === rebalance_grid_orders函数开始执行 ===
2025-07-18 01:35:30,452 - INFO - [rebalance_grid_orders] - 开始状态同步 (锁外)...
2025-07-18 01:35:34,117 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 173.0
2025-07-18 01:35:34,118 - INFO - [_internal_state_sync] - 同步到 0 个有效挂单
2025-07-18 01:35:34,118 - INFO - [rebalance_grid_orders] - 状态同步完成 (锁外)
2025-07-19 11:08:52,557 - INFO - [main] - 启动前预检 API 连接...
2025-07-19 11:08:52,558 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-19 11:08:52,565 - INFO - [main] - 测试代理连接: http://httpbin.org/ip
2025-07-19 11:08:53,612 - INFO - [main] - 代理服务器连接正常 (测试URL: http://httpbin.org/ip)
2025-07-19 11:08:53,613 - INFO - [main] - 代理服务器测试通过
2025-07-19 11:08:53,618 - INFO - [main] - 正在测试 API 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/5
2025-07-19 11:08:53,701 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.08s
2025-07-19 11:08:53,702 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-19 11:08:53,702 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-07-19 11:08:53,708 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-19 11:08:53,708 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-19 11:08:53,710 - INFO - [setup] - 正在执行启动设置...
2025-07-19 11:08:53,710 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-19 11:08:53,710 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-19 11:08:53,715 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-19 11:08:53,798 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-19 11:08:53,799 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-19 11:08:54,298 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-19 11:08:54,298 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-19 11:08:54,625 - INFO - [test_connection] - 服务器时间获取成功: 1752894533690
2025-07-19 11:08:54,625 - INFO - [test_connection] - 正在加载市场数据...
2025-07-19 11:08:58,654 - INFO - [test_connection] - 市场数据加载成功
2025-07-19 11:08:58,654 - INFO - [test_connection] - 交易对 SOL/USDT:USDT 验证成功
2025-07-19 11:08:58,654 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-19 11:08:59,920 - INFO - [test_connection] - Ticker 获取成功: SOL/USDT:USDT @ 177.46
2025-07-19 11:08:59,920 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-19 11:09:01,179 - INFO - [test_connection] - 余额获取成功，USDT余额: 1656.93173557
2025-07-19 11:09:01,179 - INFO - [test_connection] - API连接测试完成！
2025-07-19 11:09:01,179 - INFO - [setup] - 正在加载市场数据...
2025-07-19 11:09:05,265 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-19 11:09:06,520 - INFO - [set_hedge_mode] - 当前已是双向持仓模式，无需更改
2025-07-19 11:09:07,787 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-19 11:09:12,660 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.4
2025-07-19 11:09:12,660 - INFO - [_internal_state_sync] - 同步到 0 个有效挂单
2025-07-19 11:09:16,383 - INFO - [fetch_listen_key] - 成功获取新的 listen key: UnGF2yiQTi...
2025-07-19 11:09:16,384 - INFO - [setup] - 初始化设置完成。
2025-07-19 11:09:16,384 - INFO - [setup] - 当前持仓: 多头=0.0, 空头=0.0
2025-07-19 11:09:20,051 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.26
2025-07-19 11:09:20,051 - INFO - [_internal_state_sync] - 同步到 0 个有效挂单
2025-07-19 11:09:20,051 - INFO - [cancel_all_open_orders] - 正在尝试取消所有挂单...
2025-07-19 11:09:21,317 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-19 11:09:21,317 - INFO - [run] - 所有现有挂单已取消，准备启动策略...
2025-07-19 11:09:21,317 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/UnGF2yiQTi7gd26rwn8nqR54pDF3RWhj1dfg3J8s8otTux8QwmrJWnVBXSoaeGFT
2025-07-19 11:09:21,537 - INFO - [run] - WebSocket 连接成功。
2025-07-19 11:09:21,537 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdt@bookTicker'], 'id': 1}
2025-07-19 11:09:25,195 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.19
2025-07-19 11:09:25,196 - INFO - [_internal_state_sync] - 同步到 0 个有效挂单
2025-07-19 11:09:25,198 - INFO - [execute_argm_strategy] - 网格重绘触发！新中枢: 172.8177, 新范围: [166.9665, 178.6689]
2025-07-19 11:09:25,199 - INFO - [rebalance_grid_orders] - === rebalance_grid_orders函数开始执行 ===
2025-07-19 11:09:25,199 - INFO - [rebalance_grid_orders] - 开始状态同步 (锁外)...
2025-07-19 11:09:28,861 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.17
2025-07-19 11:09:28,862 - INFO - [_internal_state_sync] - 同步到 0 个有效挂单
2025-07-19 11:09:28,862 - INFO - [rebalance_grid_orders] - 状态同步完成 (锁外)
2025-07-19 11:16:56,952 - INFO - [main] - 启动前预检 API 连接...
2025-07-19 11:16:56,952 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-19 11:16:56,963 - INFO - [main] - 测试代理连接: http://httpbin.org/ip
2025-07-19 11:16:58,263 - INFO - [main] - 代理服务器连接正常 (测试URL: http://httpbin.org/ip)
2025-07-19 11:16:58,264 - INFO - [main] - 代理服务器测试通过
2025-07-19 11:16:58,268 - INFO - [main] - 正在测试 API 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/5
2025-07-19 11:16:58,356 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.09s
2025-07-19 11:16:58,356 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-19 11:16:58,356 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-07-19 11:16:58,364 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-19 11:16:58,364 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-19 11:16:58,365 - INFO - [setup] - 正在执行启动设置...
2025-07-19 11:16:58,365 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-19 11:16:58,365 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-19 11:16:58,370 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-19 11:16:58,453 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-19 11:16:58,453 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-19 11:16:58,925 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-19 11:16:58,925 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-19 11:16:59,262 - INFO - [test_connection] - 服务器时间获取成功: 1752895018327
2025-07-19 11:16:59,262 - INFO - [test_connection] - 正在加载市场数据...
2025-07-19 11:17:03,284 - INFO - [test_connection] - 市场数据加载成功
2025-07-19 11:17:03,284 - INFO - [test_connection] - 交易对 SOL/USDT:USDT 验证成功
2025-07-19 11:17:03,284 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-19 11:17:04,550 - INFO - [test_connection] - Ticker 获取成功: SOL/USDT:USDT @ 177.36
2025-07-19 11:17:04,550 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-19 11:17:05,805 - INFO - [test_connection] - 余额获取成功，USDT余额: 1656.93173557
2025-07-19 11:17:05,806 - INFO - [test_connection] - API连接测试完成！
2025-07-19 11:17:05,806 - INFO - [setup] - 正在加载市场数据...
2025-07-19 11:17:09,904 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-19 11:17:11,174 - INFO - [set_hedge_mode] - 当前已是双向持仓模式，无需更改
2025-07-19 11:17:12,438 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-19 11:17:17,301 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.36
2025-07-19 11:17:17,301 - INFO - [_internal_state_sync] - 同步到 0 个有效挂单
2025-07-19 11:17:21,036 - INFO - [fetch_listen_key] - 成功获取新的 listen key: UnGF2yiQTi...
2025-07-19 11:17:21,037 - INFO - [setup] - 初始化设置完成。
2025-07-19 11:17:21,037 - INFO - [setup] - 当前持仓: 多头=0.0, 空头=0.0
2025-07-19 11:17:24,695 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.39
2025-07-19 11:17:24,695 - INFO - [_internal_state_sync] - 同步到 0 个有效挂单
2025-07-19 11:17:24,695 - INFO - [cancel_all_open_orders] - 正在尝试取消所有挂单...
2025-07-19 11:17:25,971 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-19 11:17:25,972 - INFO - [run] - 所有现有挂单已取消，准备启动策略...
2025-07-19 11:17:25,972 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/UnGF2yiQTi7gd26rwn8nqR54pDF3RWhj1dfg3J8s8otTux8QwmrJWnVBXSoaeGFT
2025-07-19 11:17:26,185 - INFO - [run] - WebSocket 连接成功。
2025-07-19 11:17:26,186 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdt@bookTicker'], 'id': 1}
2025-07-19 11:17:29,834 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.44
2025-07-19 11:17:29,834 - INFO - [_internal_state_sync] - 同步到 0 个有效挂单
2025-07-19 11:17:29,837 - INFO - [execute_argm_strategy] - 网格重绘触发！新中枢: 172.8200, 新范围: [166.9495, 178.6904]
2025-07-19 11:17:29,837 - INFO - [rebalance_grid_orders] - === rebalance_grid_orders函数开始执行 ===
2025-07-19 11:17:29,837 - INFO - [rebalance_grid_orders] - 开始状态同步 (锁外)...
2025-07-19 11:17:33,503 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.44
2025-07-19 11:17:33,503 - INFO - [_internal_state_sync] - 同步到 0 个有效挂单
2025-07-19 11:17:33,503 - INFO - [rebalance_grid_orders] - 状态同步完成 (锁外)
2025-07-19 11:17:33,503 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-19 11:17:33,503 - INFO - [rebalance_grid_orders] - 调试信息 - 交易方向: LONG_ONLY, 当前价格: 177.44, 最佳买价: 177.4, 最佳卖价: 177.41
2025-07-19 11:17:33,503 - INFO - [rebalance_grid_orders] - 调试信息 - 现有订单价格: set()
2025-07-19 11:17:33,503 - INFO - [rebalance_grid_orders] - 调试信息 - 目标网格价格: [167.03, 167.06, 167.11, 167.17, 167.26, 167.38, 167.54, 167.77, 168.08, 168.48, 169.0, 169.65, 170.44, 171.33, 172.32, 173.32, 174.31, 175.2, 175.99, 176.64, 177.16, 177.56, 177.87, 178.1, 178.26, 178.38, 178.47, 178.53, 178.58, 178.61]
2025-07-19 11:17:33,503 - INFO - [rebalance_grid_orders] - 调试信息 - 识别到需要下单的订单数量: 21
2025-07-19 11:17:33,504 - INFO - [rebalance_grid_orders] - 调试信息 - 待下单详情: [{'side': 'buy', 'price': 167.03, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 167.06, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 167.11, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 167.17, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 167.26, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 167.38, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 167.54, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 167.77, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 168.08, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 168.48, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 169.0, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 169.65, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 170.44, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 171.33, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 172.32, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 173.32, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 174.31, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 175.2, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 175.99, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 176.64, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 177.16, 'positionSide': 'LONG'}]
2025-07-19 11:17:33,504 - INFO - [rebalance_grid_orders] - 开始执行订单操作: 取消0个, 创建21个
2025-07-19 11:17:33,504 - INFO - [rebalance_grid_orders] - 准备创建 21 个新订单
2025-07-19 11:17:33,504 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 167.0300
2025-07-19 11:17:34,778 - INFO - [place_order] - 下单成功: x-argm-1752895053504-2d8a
2025-07-19 11:17:34,859 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 167.0600
2025-07-19 11:17:39,726 - INFO - [place_order] - 下单成功: x-argm-1752895054859-4061
2025-07-19 11:17:39,813 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 167.1100
2025-07-19 11:17:44,677 - INFO - [place_order] - 下单成功: x-argm-1752895059813-c08c
2025-07-19 11:17:44,785 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 167.1700
2025-07-19 11:19:59,164 - INFO - [main] - 启动前预检 API 连接...
2025-07-19 11:19:59,164 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-19 11:19:59,173 - INFO - [main] - 测试代理连接: http://httpbin.org/ip
2025-07-19 11:19:59,756 - INFO - [main] - 代理服务器连接正常 (测试URL: http://httpbin.org/ip)
2025-07-19 11:19:59,756 - INFO - [main] - 代理服务器测试通过
2025-07-19 11:19:59,759 - INFO - [main] - 正在测试 API 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/5
2025-07-19 11:19:59,841 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.08s
2025-07-19 11:19:59,841 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-19 11:19:59,841 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-07-19 11:19:59,848 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-19 11:19:59,848 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-19 11:19:59,849 - INFO - [setup] - 正在执行启动设置...
2025-07-19 11:19:59,849 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-19 11:19:59,849 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-19 11:19:59,853 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-19 11:19:59,935 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-19 11:19:59,935 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-19 11:20:00,390 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-19 11:20:00,391 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-19 11:20:00,727 - INFO - [test_connection] - 服务器时间获取成功: 1752895199794
2025-07-19 11:20:00,728 - INFO - [test_connection] - 正在加载市场数据...
2025-07-19 11:20:04,890 - INFO - [test_connection] - 市场数据加载成功
2025-07-19 11:20:04,890 - INFO - [test_connection] - 交易对 SOL/USDT:USDT 验证成功
2025-07-19 11:20:04,890 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-19 11:20:06,162 - INFO - [test_connection] - Ticker 获取成功: SOL/USDT:USDT @ 177.44
2025-07-19 11:20:06,162 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-19 11:20:07,423 - INFO - [test_connection] - 余额获取成功，USDT余额: 1656.93173557
2025-07-19 11:20:07,423 - INFO - [test_connection] - API连接测试完成！
2025-07-19 11:20:07,423 - INFO - [setup] - 正在加载市场数据...
2025-07-19 11:20:11,509 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-19 11:20:12,773 - INFO - [set_hedge_mode] - 当前已是双向持仓模式，无需更改
2025-07-19 11:20:14,034 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-19 11:20:18,903 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.43
2025-07-19 11:20:18,903 - INFO - [_internal_state_sync] - 同步到 3 个有效挂单
2025-07-19 11:20:22,634 - INFO - [fetch_listen_key] - 成功获取新的 listen key: UnGF2yiQTi...
2025-07-19 11:20:22,634 - INFO - [setup] - 初始化设置完成。
2025-07-19 11:20:22,635 - INFO - [setup] - 当前持仓: 多头=0.0, 空头=0.0
2025-07-19 11:20:26,296 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.44
2025-07-19 11:20:26,296 - INFO - [_internal_state_sync] - 同步到 0 个有效挂单
2025-07-19 11:20:26,296 - INFO - [cancel_all_open_orders] - 正在尝试取消所有挂单...
2025-07-19 11:20:27,573 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-19 11:20:27,574 - INFO - [run] - 所有现有挂单已取消，准备启动策略...
2025-07-19 11:20:27,574 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/UnGF2yiQTi7gd26rwn8nqR54pDF3RWhj1dfg3J8s8otTux8QwmrJWnVBXSoaeGFT
2025-07-19 11:20:27,784 - INFO - [run] - WebSocket 连接成功。
2025-07-19 11:20:27,785 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdt@bookTicker'], 'id': 1}
2025-07-19 11:20:31,435 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.45
2025-07-19 11:20:31,435 - INFO - [_internal_state_sync] - 同步到 0 个有效挂单
2025-07-19 11:20:31,437 - INFO - [execute_argm_strategy] - 网格重绘触发！新中枢: 172.8214, 新范围: [166.9344, 178.7084]
2025-07-19 11:20:31,438 - INFO - [rebalance_grid_orders] - === rebalance_grid_orders函数开始执行 ===
2025-07-19 11:20:31,438 - INFO - [rebalance_grid_orders] - 开始状态同步 (锁外)...
2025-07-19 11:20:35,109 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.43
2025-07-19 11:20:35,110 - INFO - [_internal_state_sync] - 同步到 0 个有效挂单
2025-07-19 11:20:35,111 - INFO - [rebalance_grid_orders] - 状态同步完成 (锁外)
2025-07-19 11:20:35,111 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-19 11:20:35,111 - INFO - [rebalance_grid_orders] - 调试信息 - 交易方向: LONG_ONLY, 当前价格: 177.43, 最佳买价: 177.45, 最佳卖价: 177.46
2025-07-19 11:20:35,111 - INFO - [rebalance_grid_orders] - 调试信息 - 现有订单价格: set()
2025-07-19 11:20:35,112 - INFO - [rebalance_grid_orders] - 调试信息 - 目标网格价格: [167.01, 167.05, 167.09, 167.15, 167.24, 167.36, 167.53, 167.76, 168.07, 168.47, 168.99, 169.65, 170.43, 171.33, 172.32, 173.33, 174.31, 175.21, 176.0, 176.65, 177.17, 177.58, 177.88, 178.11, 178.28, 178.4, 178.49, 178.55, 178.6, 178.63]
2025-07-19 11:20:35,112 - INFO - [rebalance_grid_orders] - 调试信息 - 识别到需要下单的订单数量: 21
2025-07-19 11:20:35,112 - INFO - [rebalance_grid_orders] - 调试信息 - 待下单详情: [{'side': 'buy', 'price': 167.01, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 167.05, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 167.09, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 167.15, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 167.24, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 167.36, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 167.53, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 167.76, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 168.07, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 168.47, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 168.99, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 169.65, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 170.43, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 171.33, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 172.32, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 173.33, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 174.31, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 175.21, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 176.0, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 176.65, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 177.17, 'positionSide': 'LONG'}]
2025-07-19 11:20:35,112 - INFO - [rebalance_grid_orders] - 开始执行订单操作: 取消0个, 创建21个
2025-07-19 11:20:35,112 - INFO - [rebalance_grid_orders] - 准备创建 21 个新订单
2025-07-19 11:20:35,113 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 167.0100
2025-07-19 11:20:36,367 - INFO - [place_order] - 下单成功: x-argm-1752895235113-24b6
2025-07-19 11:20:36,457 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 167.0500
2025-07-19 11:20:41,326 - INFO - [place_order] - 下单成功: x-argm-1752895236457-4643
2025-07-19 11:20:41,433 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 167.0900
2025-07-19 11:20:46,292 - INFO - [place_order] - 下单成功: x-argm-1752895241433-c9b0
2025-07-19 11:20:46,397 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 167.1500
2025-07-19 11:20:51,324 - INFO - [place_order] - 下单成功: x-argm-1752895246397-7462
2025-07-19 11:20:51,433 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 167.2400
2025-07-19 11:20:56,298 - INFO - [place_order] - 下单成功: x-argm-1752895251433-6395
2025-07-19 11:20:56,404 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 167.3600
2025-07-19 11:21:01,265 - INFO - [place_order] - 下单成功: x-argm-1752895256404-29d9
2025-07-19 11:21:01,362 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 167.5300
2025-07-19 11:21:06,229 - INFO - [place_order] - 下单成功: x-argm-1752895261362-ef89
2025-07-19 11:21:06,332 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 167.7600
2025-07-19 11:21:11,196 - INFO - [place_order] - 下单成功: x-argm-1752895266332-60ec
2025-07-19 11:21:11,298 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 168.0700
2025-07-19 11:21:16,160 - INFO - [place_order] - 下单成功: x-argm-1752895271298-f305
2025-07-19 11:21:16,273 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 168.4700
2025-07-19 11:21:21,141 - INFO - [place_order] - 下单成功: x-argm-1752895276273-367f
2025-07-19 11:21:21,245 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 168.9900
2025-07-19 11:21:26,111 - INFO - [place_order] - 下单成功: x-argm-1752895281245-f078
2025-07-19 11:21:26,214 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 169.6500
2025-07-19 11:21:31,078 - INFO - [place_order] - 下单成功: x-argm-1752895286214-1780
2025-07-19 11:21:31,181 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 170.4300
2025-07-19 11:21:36,050 - INFO - [place_order] - 下单成功: x-argm-1752895291181-9514
2025-07-19 11:21:36,154 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 171.3300
2025-07-19 11:21:41,009 - INFO - [place_order] - 下单成功: x-argm-1752895296154-fb9e
2025-07-19 11:21:41,119 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 172.3200
2025-07-19 11:21:45,987 - INFO - [place_order] - 下单成功: x-argm-1752895301119-0e15
2025-07-19 11:21:46,092 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 173.3300
2025-07-19 11:21:50,956 - INFO - [place_order] - 下单成功: x-argm-1752895306092-4ebb
2025-07-19 11:21:51,060 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 174.3100
2025-07-19 11:21:55,927 - INFO - [place_order] - 下单成功: x-argm-1752895311060-88dc
2025-07-19 11:21:56,028 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 175.2100
2025-07-19 11:22:00,891 - INFO - [place_order] - 下单成功: x-argm-1752895316028-c02a
2025-07-19 11:22:01,005 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 176.0000
2025-07-19 11:22:05,865 - INFO - [place_order] - 下单成功: x-argm-1752895321005-680c
2025-07-19 11:22:05,979 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 176.6500
2025-07-19 11:22:10,849 - INFO - [place_order] - 下单成功: x-argm-1752895325979-bf06
2025-07-19 11:22:10,965 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 177.1700
2025-07-19 11:22:15,821 - INFO - [place_order] - 下单成功: x-argm-1752895330965-e588
2025-07-19 11:22:15,931 - INFO - [rebalance_grid_orders] - 成功创建 21/21 个订单
2025-07-19 11:22:23,192 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.43
2025-07-19 11:22:23,192 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:22:23,192 - INFO - [rebalance_grid_orders] - === rebalance_grid_orders函数正常完成 ===
2025-07-19 11:22:23,192 - INFO - [rebalance_grid_orders] - === rebalance_grid_orders函数开始执行 ===
2025-07-19 11:22:23,192 - INFO - [rebalance_grid_orders] - 开始状态同步 (锁外)...
2025-07-19 11:22:26,862 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.43
2025-07-19 11:22:26,862 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:22:26,862 - INFO - [rebalance_grid_orders] - 状态同步完成 (锁外)
2025-07-19 11:22:26,862 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有21个订单 -> 目标30个订单
2025-07-19 11:22:26,862 - INFO - [rebalance_grid_orders] - 调试信息 - 交易方向: LONG_ONLY, 当前价格: 177.43, 最佳买价: 177.45, 最佳卖价: 177.46
2025-07-19 11:22:26,862 - INFO - [rebalance_grid_orders] - 调试信息 - 现有订单价格: {167.24, 167.05, 167.15, 167.09, 167.01, 167.36, 167.53, 167.76, 168.07, 168.47, 168.99, 169.65, 170.43, 171.33, 172.32, 173.33, 174.31, 175.21, 176.0, 177.17, 176.65}
2025-07-19 11:22:26,862 - INFO - [rebalance_grid_orders] - 调试信息 - 目标网格价格: [167.01, 167.05, 167.09, 167.15, 167.24, 167.36, 167.53, 167.76, 168.07, 168.47, 168.99, 169.65, 170.43, 171.33, 172.32, 173.33, 174.31, 175.21, 176.0, 176.65, 177.17, 177.58, 177.88, 178.11, 178.28, 178.4, 178.49, 178.55, 178.6, 178.63]
2025-07-19 11:22:26,862 - INFO - [rebalance_grid_orders] - 调试信息 - 识别到需要下单的订单数量: 0
2025-07-19 11:22:26,862 - INFO - [rebalance_grid_orders] - 开始执行订单操作: 取消0个, 创建0个
2025-07-19 11:22:30,529 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.43
2025-07-19 11:22:30,529 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:22:30,529 - INFO - [rebalance_grid_orders] - === rebalance_grid_orders函数正常完成 ===
2025-07-19 11:22:30,529 - INFO - [rebalance_grid_orders] - === rebalance_grid_orders函数开始执行 ===
2025-07-19 11:22:30,529 - INFO - [rebalance_grid_orders] - 开始状态同步 (锁外)...
2025-07-19 11:22:34,181 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.43
2025-07-19 11:22:34,182 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:22:34,182 - INFO - [rebalance_grid_orders] - 状态同步完成 (锁外)
2025-07-19 11:22:34,182 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有21个订单 -> 目标30个订单
2025-07-19 11:22:34,182 - INFO - [rebalance_grid_orders] - 调试信息 - 交易方向: LONG_ONLY, 当前价格: 177.43, 最佳买价: 177.45, 最佳卖价: 177.46
2025-07-19 11:22:34,182 - INFO - [rebalance_grid_orders] - 调试信息 - 现有订单价格: {167.24, 167.05, 167.15, 167.09, 167.01, 167.36, 167.53, 167.76, 168.07, 168.47, 168.99, 169.65, 170.43, 171.33, 172.32, 173.33, 174.31, 175.21, 176.0, 177.17, 176.65}
2025-07-19 11:22:34,182 - INFO - [rebalance_grid_orders] - 调试信息 - 目标网格价格: [167.01, 167.05, 167.09, 167.15, 167.24, 167.36, 167.53, 167.76, 168.07, 168.47, 168.99, 169.65, 170.43, 171.33, 172.32, 173.33, 174.31, 175.21, 176.0, 176.65, 177.17, 177.58, 177.88, 178.11, 178.28, 178.4, 178.49, 178.55, 178.6, 178.63]
2025-07-19 11:22:34,182 - INFO - [rebalance_grid_orders] - 调试信息 - 识别到需要下单的订单数量: 0
2025-07-19 11:22:34,182 - INFO - [rebalance_grid_orders] - 开始执行订单操作: 取消0个, 创建0个
2025-07-19 11:22:37,848 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.39
2025-07-19 11:22:37,849 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:22:37,849 - INFO - [rebalance_grid_orders] - === rebalance_grid_orders函数正常完成 ===
2025-07-19 11:22:37,849 - INFO - [rebalance_grid_orders] - === rebalance_grid_orders函数开始执行 ===
2025-07-19 11:22:37,849 - INFO - [rebalance_grid_orders] - 开始状态同步 (锁外)...
2025-07-19 11:22:41,514 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.43
2025-07-19 11:22:41,514 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:22:41,514 - INFO - [rebalance_grid_orders] - 状态同步完成 (锁外)
2025-07-19 11:22:41,514 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有21个订单 -> 目标30个订单
2025-07-19 11:22:41,515 - INFO - [rebalance_grid_orders] - 调试信息 - 交易方向: LONG_ONLY, 当前价格: 177.43, 最佳买价: 177.45, 最佳卖价: 177.46
2025-07-19 11:22:41,515 - INFO - [rebalance_grid_orders] - 调试信息 - 现有订单价格: {167.24, 167.05, 167.15, 167.09, 167.01, 167.36, 167.53, 167.76, 168.07, 168.47, 168.99, 169.65, 170.43, 171.33, 172.32, 173.33, 174.31, 175.21, 176.0, 177.17, 176.65}
2025-07-19 11:22:41,515 - INFO - [rebalance_grid_orders] - 调试信息 - 目标网格价格: [167.01, 167.05, 167.09, 167.15, 167.24, 167.36, 167.53, 167.76, 168.07, 168.47, 168.99, 169.65, 170.43, 171.33, 172.32, 173.33, 174.31, 175.21, 176.0, 176.65, 177.17, 177.58, 177.88, 178.11, 178.28, 178.4, 178.49, 178.55, 178.6, 178.63]
2025-07-19 11:22:41,515 - INFO - [rebalance_grid_orders] - 调试信息 - 识别到需要下单的订单数量: 0
2025-07-19 11:22:41,515 - INFO - [rebalance_grid_orders] - 开始执行订单操作: 取消0个, 创建0个
2025-07-19 11:22:45,192 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.43
2025-07-19 11:22:45,192 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:22:45,192 - INFO - [rebalance_grid_orders] - === rebalance_grid_orders函数正常完成 ===
2025-07-19 11:22:45,192 - INFO - [rebalance_grid_orders] - === rebalance_grid_orders函数开始执行 ===
2025-07-19 11:22:45,192 - INFO - [rebalance_grid_orders] - 开始状态同步 (锁外)...
2025-07-19 11:22:48,855 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.42
2025-07-19 11:22:48,855 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:22:48,855 - INFO - [rebalance_grid_orders] - 状态同步完成 (锁外)
2025-07-19 11:22:48,855 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有21个订单 -> 目标30个订单
2025-07-19 11:22:48,855 - INFO - [rebalance_grid_orders] - 调试信息 - 交易方向: LONG_ONLY, 当前价格: 177.42, 最佳买价: 177.45, 最佳卖价: 177.46
2025-07-19 11:22:48,856 - INFO - [rebalance_grid_orders] - 调试信息 - 现有订单价格: {167.24, 167.05, 167.15, 167.09, 167.01, 167.36, 167.53, 167.76, 168.07, 168.47, 168.99, 169.65, 170.43, 171.33, 172.32, 173.33, 174.31, 175.21, 176.0, 177.17, 176.65}
2025-07-19 11:22:48,856 - INFO - [rebalance_grid_orders] - 调试信息 - 目标网格价格: [167.01, 167.05, 167.09, 167.15, 167.24, 167.36, 167.53, 167.76, 168.07, 168.47, 168.99, 169.65, 170.43, 171.33, 172.32, 173.33, 174.31, 175.21, 176.0, 176.65, 177.17, 177.58, 177.88, 178.11, 178.28, 178.4, 178.49, 178.55, 178.6, 178.63]
2025-07-19 11:22:48,856 - INFO - [rebalance_grid_orders] - 调试信息 - 识别到需要下单的订单数量: 0
2025-07-19 11:22:48,856 - INFO - [rebalance_grid_orders] - 开始执行订单操作: 取消0个, 创建0个
2025-07-19 11:22:52,518 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.44
2025-07-19 11:22:52,518 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:22:52,518 - INFO - [rebalance_grid_orders] - === rebalance_grid_orders函数正常完成 ===
2025-07-19 11:22:52,518 - INFO - [rebalance_grid_orders] - === rebalance_grid_orders函数开始执行 ===
2025-07-19 11:22:52,518 - INFO - [rebalance_grid_orders] - 开始状态同步 (锁外)...
2025-07-19 11:22:56,180 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.43
2025-07-19 11:22:56,180 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:22:56,181 - INFO - [rebalance_grid_orders] - 状态同步完成 (锁外)
2025-07-19 11:22:56,181 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有21个订单 -> 目标30个订单
2025-07-19 11:22:56,182 - INFO - [rebalance_grid_orders] - 调试信息 - 交易方向: LONG_ONLY, 当前价格: 177.43, 最佳买价: 177.45, 最佳卖价: 177.46
2025-07-19 11:22:56,182 - INFO - [rebalance_grid_orders] - 调试信息 - 现有订单价格: {167.24, 167.05, 167.15, 167.09, 167.01, 167.36, 167.53, 167.76, 168.07, 168.47, 168.99, 169.65, 170.43, 171.33, 172.32, 173.33, 174.31, 175.21, 176.0, 177.17, 176.65}
2025-07-19 11:22:56,182 - INFO - [rebalance_grid_orders] - 调试信息 - 目标网格价格: [167.01, 167.05, 167.09, 167.15, 167.24, 167.36, 167.53, 167.76, 168.07, 168.47, 168.99, 169.65, 170.43, 171.33, 172.32, 173.33, 174.31, 175.21, 176.0, 176.65, 177.17, 177.58, 177.88, 178.11, 178.28, 178.4, 178.49, 178.55, 178.6, 178.63]
2025-07-19 11:22:56,182 - INFO - [rebalance_grid_orders] - 调试信息 - 识别到需要下单的订单数量: 0
2025-07-19 11:22:56,183 - INFO - [rebalance_grid_orders] - 开始执行订单操作: 取消0个, 创建0个
2025-07-19 11:22:59,847 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.39
2025-07-19 11:22:59,847 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:22:59,847 - INFO - [rebalance_grid_orders] - === rebalance_grid_orders函数正常完成 ===
2025-07-19 11:22:59,847 - INFO - [rebalance_grid_orders] - === rebalance_grid_orders函数开始执行 ===
2025-07-19 11:22:59,848 - INFO - [rebalance_grid_orders] - 开始状态同步 (锁外)...
2025-07-19 11:23:03,502 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.39
2025-07-19 11:23:03,504 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:23:03,504 - INFO - [rebalance_grid_orders] - 状态同步完成 (锁外)
2025-07-19 11:23:03,504 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有21个订单 -> 目标30个订单
2025-07-19 11:23:03,504 - INFO - [rebalance_grid_orders] - 调试信息 - 交易方向: LONG_ONLY, 当前价格: 177.39, 最佳买价: 177.45, 最佳卖价: 177.46
2025-07-19 11:23:03,504 - INFO - [rebalance_grid_orders] - 调试信息 - 现有订单价格: {167.24, 167.05, 167.15, 167.09, 167.01, 167.36, 167.53, 167.76, 168.07, 168.47, 168.99, 169.65, 170.43, 171.33, 172.32, 173.33, 174.31, 175.21, 176.0, 177.17, 176.65}
2025-07-19 11:23:03,505 - INFO - [rebalance_grid_orders] - 调试信息 - 目标网格价格: [167.01, 167.05, 167.09, 167.15, 167.24, 167.36, 167.53, 167.76, 168.07, 168.47, 168.99, 169.65, 170.43, 171.33, 172.32, 173.33, 174.31, 175.21, 176.0, 176.65, 177.17, 177.58, 177.88, 178.11, 178.28, 178.4, 178.49, 178.55, 178.6, 178.63]
2025-07-19 11:23:03,505 - INFO - [rebalance_grid_orders] - 调试信息 - 识别到需要下单的订单数量: 0
2025-07-19 11:23:03,505 - INFO - [rebalance_grid_orders] - 开始执行订单操作: 取消0个, 创建0个
2025-07-19 11:23:07,174 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.4
2025-07-19 11:23:07,175 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:23:07,175 - INFO - [rebalance_grid_orders] - === rebalance_grid_orders函数正常完成 ===
2025-07-19 11:23:07,175 - INFO - [rebalance_grid_orders] - === rebalance_grid_orders函数开始执行 ===
2025-07-19 11:23:07,175 - INFO - [rebalance_grid_orders] - 开始状态同步 (锁外)...
2025-07-19 11:23:10,829 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.4
2025-07-19 11:23:10,830 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:23:10,831 - INFO - [rebalance_grid_orders] - 状态同步完成 (锁外)
2025-07-19 11:23:10,831 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有21个订单 -> 目标30个订单
2025-07-19 11:23:10,832 - INFO - [rebalance_grid_orders] - 调试信息 - 交易方向: LONG_ONLY, 当前价格: 177.4, 最佳买价: 177.45, 最佳卖价: 177.46
2025-07-19 11:23:10,833 - INFO - [rebalance_grid_orders] - 调试信息 - 现有订单价格: {167.24, 167.05, 167.15, 167.09, 167.01, 167.36, 167.53, 167.76, 168.07, 168.47, 168.99, 169.65, 170.43, 171.33, 172.32, 173.33, 174.31, 175.21, 176.0, 177.17, 176.65}
2025-07-19 11:23:10,833 - INFO - [rebalance_grid_orders] - 调试信息 - 目标网格价格: [167.01, 167.05, 167.09, 167.15, 167.24, 167.36, 167.53, 167.76, 168.07, 168.47, 168.99, 169.65, 170.43, 171.33, 172.32, 173.33, 174.31, 175.21, 176.0, 176.65, 177.17, 177.58, 177.88, 178.11, 178.28, 178.4, 178.49, 178.55, 178.6, 178.63]
2025-07-19 11:23:10,833 - INFO - [rebalance_grid_orders] - 调试信息 - 识别到需要下单的订单数量: 0
2025-07-19 11:23:10,833 - INFO - [rebalance_grid_orders] - 开始执行订单操作: 取消0个, 创建0个
2025-07-19 11:23:14,500 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.4
2025-07-19 11:23:14,501 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:23:14,501 - INFO - [rebalance_grid_orders] - === rebalance_grid_orders函数正常完成 ===
2025-07-19 11:23:14,501 - INFO - [rebalance_grid_orders] - === rebalance_grid_orders函数开始执行 ===
2025-07-19 11:23:14,501 - INFO - [rebalance_grid_orders] - 开始状态同步 (锁外)...
2025-07-19 11:23:18,182 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.39
2025-07-19 11:23:18,182 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:23:18,183 - INFO - [rebalance_grid_orders] - 状态同步完成 (锁外)
2025-07-19 11:23:18,183 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有21个订单 -> 目标30个订单
2025-07-19 11:23:18,183 - INFO - [rebalance_grid_orders] - 调试信息 - 交易方向: LONG_ONLY, 当前价格: 177.39, 最佳买价: 177.45, 最佳卖价: 177.46
2025-07-19 11:23:18,184 - INFO - [rebalance_grid_orders] - 调试信息 - 现有订单价格: {167.24, 167.05, 167.15, 167.09, 167.01, 167.36, 167.53, 167.76, 168.07, 168.47, 168.99, 169.65, 170.43, 171.33, 172.32, 173.33, 174.31, 175.21, 176.0, 177.17, 176.65}
2025-07-19 11:23:18,185 - INFO - [rebalance_grid_orders] - 调试信息 - 目标网格价格: [167.01, 167.05, 167.09, 167.15, 167.24, 167.36, 167.53, 167.76, 168.07, 168.47, 168.99, 169.65, 170.43, 171.33, 172.32, 173.33, 174.31, 175.21, 176.0, 176.65, 177.17, 177.58, 177.88, 178.11, 178.28, 178.4, 178.49, 178.55, 178.6, 178.63]
2025-07-19 11:23:18,185 - INFO - [rebalance_grid_orders] - 调试信息 - 识别到需要下单的订单数量: 0
2025-07-19 11:23:18,185 - INFO - [rebalance_grid_orders] - 开始执行订单操作: 取消0个, 创建0个
2025-07-19 11:23:21,854 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.39
2025-07-19 11:23:21,854 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:23:21,854 - INFO - [rebalance_grid_orders] - === rebalance_grid_orders函数正常完成 ===
2025-07-19 11:23:21,854 - INFO - [rebalance_grid_orders] - === rebalance_grid_orders函数开始执行 ===
2025-07-19 11:23:21,854 - INFO - [rebalance_grid_orders] - 开始状态同步 (锁外)...
2025-07-19 11:23:25,507 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.39
2025-07-19 11:23:25,508 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:23:25,508 - INFO - [rebalance_grid_orders] - 状态同步完成 (锁外)
2025-07-19 11:23:25,508 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有21个订单 -> 目标30个订单
2025-07-19 11:23:25,508 - INFO - [rebalance_grid_orders] - 调试信息 - 交易方向: LONG_ONLY, 当前价格: 177.39, 最佳买价: 177.45, 最佳卖价: 177.46
2025-07-19 11:23:25,509 - INFO - [rebalance_grid_orders] - 调试信息 - 现有订单价格: {167.24, 167.05, 167.15, 167.09, 167.01, 167.36, 167.53, 167.76, 168.07, 168.47, 168.99, 169.65, 170.43, 171.33, 172.32, 173.33, 174.31, 175.21, 176.0, 177.17, 176.65}
2025-07-19 11:23:25,509 - INFO - [rebalance_grid_orders] - 调试信息 - 目标网格价格: [167.01, 167.05, 167.09, 167.15, 167.24, 167.36, 167.53, 167.76, 168.07, 168.47, 168.99, 169.65, 170.43, 171.33, 172.32, 173.33, 174.31, 175.21, 176.0, 176.65, 177.17, 177.58, 177.88, 178.11, 178.28, 178.4, 178.49, 178.55, 178.6, 178.63]
2025-07-19 11:23:25,509 - INFO - [rebalance_grid_orders] - 调试信息 - 识别到需要下单的订单数量: 0
2025-07-19 11:23:25,510 - INFO - [rebalance_grid_orders] - 开始执行订单操作: 取消0个, 创建0个
2025-07-19 11:23:29,173 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.39
2025-07-19 11:23:29,173 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:23:29,173 - INFO - [rebalance_grid_orders] - === rebalance_grid_orders函数正常完成 ===
2025-07-19 11:23:29,173 - INFO - [rebalance_grid_orders] - === rebalance_grid_orders函数开始执行 ===
2025-07-19 11:23:29,173 - INFO - [rebalance_grid_orders] - 开始状态同步 (锁外)...
2025-07-19 11:23:32,840 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.37
2025-07-19 11:23:32,840 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:23:32,840 - INFO - [rebalance_grid_orders] - 状态同步完成 (锁外)
2025-07-19 11:23:32,840 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有21个订单 -> 目标30个订单
2025-07-19 11:23:32,840 - INFO - [rebalance_grid_orders] - 调试信息 - 交易方向: LONG_ONLY, 当前价格: 177.37, 最佳买价: 177.45, 最佳卖价: 177.46
2025-07-19 11:23:32,840 - INFO - [rebalance_grid_orders] - 调试信息 - 现有订单价格: {167.24, 167.05, 167.15, 167.09, 167.01, 167.36, 167.53, 167.76, 168.07, 168.47, 168.99, 169.65, 170.43, 171.33, 172.32, 173.33, 174.31, 175.21, 176.0, 177.17, 176.65}
2025-07-19 11:23:32,840 - INFO - [rebalance_grid_orders] - 调试信息 - 目标网格价格: [167.01, 167.05, 167.09, 167.15, 167.24, 167.36, 167.53, 167.76, 168.07, 168.47, 168.99, 169.65, 170.43, 171.33, 172.32, 173.33, 174.31, 175.21, 176.0, 176.65, 177.17, 177.58, 177.88, 178.11, 178.28, 178.4, 178.49, 178.55, 178.6, 178.63]
2025-07-19 11:23:32,840 - INFO - [rebalance_grid_orders] - 调试信息 - 识别到需要下单的订单数量: 0
2025-07-19 11:23:32,841 - INFO - [rebalance_grid_orders] - 开始执行订单操作: 取消0个, 创建0个
2025-07-19 11:23:36,512 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.37
2025-07-19 11:23:36,512 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:23:36,513 - INFO - [rebalance_grid_orders] - === rebalance_grid_orders函数正常完成 ===
2025-07-19 11:23:36,513 - INFO - [rebalance_grid_orders] - === rebalance_grid_orders函数开始执行 ===
2025-07-19 11:23:36,513 - INFO - [rebalance_grid_orders] - 开始状态同步 (锁外)...
2025-07-19 11:23:40,179 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.32
2025-07-19 11:23:40,179 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:23:40,179 - INFO - [rebalance_grid_orders] - 状态同步完成 (锁外)
2025-07-19 11:23:40,179 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有21个订单 -> 目标30个订单
2025-07-19 11:23:40,179 - INFO - [rebalance_grid_orders] - 调试信息 - 交易方向: LONG_ONLY, 当前价格: 177.32, 最佳买价: 177.45, 最佳卖价: 177.46
2025-07-19 11:23:40,179 - INFO - [rebalance_grid_orders] - 调试信息 - 现有订单价格: {167.24, 167.05, 167.15, 167.09, 167.01, 167.36, 167.53, 167.76, 168.07, 168.47, 168.99, 169.65, 170.43, 171.33, 172.32, 173.33, 174.31, 175.21, 176.0, 177.17, 176.65}
2025-07-19 11:23:40,179 - INFO - [rebalance_grid_orders] - 调试信息 - 目标网格价格: [167.01, 167.05, 167.09, 167.15, 167.24, 167.36, 167.53, 167.76, 168.07, 168.47, 168.99, 169.65, 170.43, 171.33, 172.32, 173.33, 174.31, 175.21, 176.0, 176.65, 177.17, 177.58, 177.88, 178.11, 178.28, 178.4, 178.49, 178.55, 178.6, 178.63]
2025-07-19 11:23:40,179 - INFO - [rebalance_grid_orders] - 调试信息 - 识别到需要下单的订单数量: 0
2025-07-19 11:23:40,180 - INFO - [rebalance_grid_orders] - 开始执行订单操作: 取消0个, 创建0个
2025-07-19 11:23:43,834 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.32
2025-07-19 11:23:43,834 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:23:43,834 - INFO - [rebalance_grid_orders] - === rebalance_grid_orders函数正常完成 ===
2025-07-19 11:23:43,834 - INFO - [rebalance_grid_orders] - === rebalance_grid_orders函数开始执行 ===
2025-07-19 11:23:43,834 - INFO - [rebalance_grid_orders] - 开始状态同步 (锁外)...
2025-07-19 11:23:47,494 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.32
2025-07-19 11:23:47,494 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:23:47,494 - INFO - [rebalance_grid_orders] - 状态同步完成 (锁外)
2025-07-19 11:23:47,494 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有21个订单 -> 目标30个订单
2025-07-19 11:23:47,494 - INFO - [rebalance_grid_orders] - 调试信息 - 交易方向: LONG_ONLY, 当前价格: 177.32, 最佳买价: 177.45, 最佳卖价: 177.46
2025-07-19 11:23:47,494 - INFO - [rebalance_grid_orders] - 调试信息 - 现有订单价格: {167.24, 167.05, 167.15, 167.09, 167.01, 167.36, 167.53, 167.76, 168.07, 168.47, 168.99, 169.65, 170.43, 171.33, 172.32, 173.33, 174.31, 175.21, 176.0, 177.17, 176.65}
2025-07-19 11:23:47,494 - INFO - [rebalance_grid_orders] - 调试信息 - 目标网格价格: [167.01, 167.05, 167.09, 167.15, 167.24, 167.36, 167.53, 167.76, 168.07, 168.47, 168.99, 169.65, 170.43, 171.33, 172.32, 173.33, 174.31, 175.21, 176.0, 176.65, 177.17, 177.58, 177.88, 178.11, 178.28, 178.4, 178.49, 178.55, 178.6, 178.63]
2025-07-19 11:23:47,494 - INFO - [rebalance_grid_orders] - 调试信息 - 识别到需要下单的订单数量: 0
2025-07-19 11:23:47,495 - INFO - [rebalance_grid_orders] - 开始执行订单操作: 取消0个, 创建0个
2025-07-19 11:23:51,156 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.32
2025-07-19 11:23:51,156 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:23:51,156 - INFO - [rebalance_grid_orders] - === rebalance_grid_orders函数正常完成 ===
2025-07-19 11:23:51,156 - INFO - [rebalance_grid_orders] - === rebalance_grid_orders函数开始执行 ===
2025-07-19 11:23:51,156 - INFO - [rebalance_grid_orders] - 开始状态同步 (锁外)...
2025-07-19 11:23:54,820 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.35
2025-07-19 11:23:54,821 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:23:54,821 - INFO - [rebalance_grid_orders] - 状态同步完成 (锁外)
2025-07-19 11:23:54,821 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有21个订单 -> 目标30个订单
2025-07-19 11:23:54,821 - INFO - [rebalance_grid_orders] - 调试信息 - 交易方向: LONG_ONLY, 当前价格: 177.35, 最佳买价: 177.45, 最佳卖价: 177.46
2025-07-19 11:23:54,821 - INFO - [rebalance_grid_orders] - 调试信息 - 现有订单价格: {167.24, 167.05, 167.15, 167.09, 167.01, 167.36, 167.53, 167.76, 168.07, 168.47, 168.99, 169.65, 170.43, 171.33, 172.32, 173.33, 174.31, 175.21, 176.0, 177.17, 176.65}
2025-07-19 11:23:54,821 - INFO - [rebalance_grid_orders] - 调试信息 - 目标网格价格: [167.01, 167.05, 167.09, 167.15, 167.24, 167.36, 167.53, 167.76, 168.07, 168.47, 168.99, 169.65, 170.43, 171.33, 172.32, 173.33, 174.31, 175.21, 176.0, 176.65, 177.17, 177.58, 177.88, 178.11, 178.28, 178.4, 178.49, 178.55, 178.6, 178.63]
2025-07-19 11:23:54,822 - INFO - [rebalance_grid_orders] - 调试信息 - 识别到需要下单的订单数量: 0
2025-07-19 11:23:54,822 - INFO - [rebalance_grid_orders] - 开始执行订单操作: 取消0个, 创建0个
2025-07-19 11:23:56,353 - INFO - [close] - 正在关闭程序...
2025-07-19 11:23:56,354 - INFO - [cancel_all_open_orders] - 正在取消 21 个挂单...
2025-07-19 11:23:59,684 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-19 11:23:59,947 - INFO - [close] - 程序已关闭。
2025-07-19 11:24:50,896 - INFO - [main] - 启动前预检 API 连接...
2025-07-19 11:24:50,896 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-19 11:24:50,902 - INFO - [main] - 测试代理连接: http://httpbin.org/ip
2025-07-19 11:24:51,242 - INFO - [main] - 代理服务器连接正常 (测试URL: http://httpbin.org/ip)
2025-07-19 11:24:51,243 - INFO - [main] - 代理服务器测试通过
2025-07-19 11:24:51,248 - INFO - [main] - 正在测试 API 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/5
2025-07-19 11:24:51,331 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.08s
2025-07-19 11:24:51,332 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-19 11:24:51,332 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-07-19 11:24:51,339 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-19 11:24:51,339 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-19 11:24:51,341 - INFO - [setup] - 正在执行启动设置...
2025-07-19 11:24:51,341 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-19 11:24:51,341 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-19 11:24:51,345 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-19 11:24:51,360 - ERROR - [test_connection] - 端点测试失败 https://fapi.binance.com/fapi/v1/ping: Cannot connect to host fapi.binance.com:443 ssl:default [None]
2025-07-19 11:24:51,361 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/time
2025-07-19 11:24:51,446 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/time - {'serverTime': 1752895490512}
2025-07-19 11:24:51,447 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-19 11:24:51,966 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-19 11:24:51,966 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-19 11:24:52,296 - INFO - [test_connection] - 服务器时间获取成功: 1752895491363
2025-07-19 11:24:52,297 - INFO - [test_connection] - 正在加载市场数据...
2025-07-19 11:24:56,324 - INFO - [test_connection] - 市场数据加载成功
2025-07-19 11:24:56,325 - INFO - [test_connection] - 交易对 SOL/USDT:USDT 验证成功
2025-07-19 11:24:56,325 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-19 11:24:57,579 - INFO - [test_connection] - Ticker 获取成功: SOL/USDT:USDT @ 177.34
2025-07-19 11:24:57,579 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-19 11:24:58,853 - INFO - [test_connection] - 余额获取成功，USDT余额: 1656.93173557
2025-07-19 11:24:58,853 - INFO - [test_connection] - API连接测试完成！
2025-07-19 11:24:58,853 - INFO - [setup] - 正在加载市场数据...
2025-07-19 11:25:02,923 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-19 11:25:04,178 - INFO - [set_hedge_mode] - 当前已是双向持仓模式，无需更改
2025-07-19 11:25:05,444 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-19 11:25:10,318 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.35
2025-07-19 11:25:10,318 - INFO - [_internal_state_sync] - 同步到 0 个有效挂单
2025-07-19 11:25:14,046 - INFO - [fetch_listen_key] - 成功获取新的 listen key: UnGF2yiQTi...
2025-07-19 11:25:14,047 - INFO - [setup] - 初始化设置完成。
2025-07-19 11:25:14,047 - INFO - [setup] - 当前持仓: 多头=0.0, 空头=0.0
2025-07-19 11:25:17,702 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.34
2025-07-19 11:25:17,702 - INFO - [_internal_state_sync] - 同步到 0 个有效挂单
2025-07-19 11:25:17,702 - INFO - [cancel_all_open_orders] - 正在尝试取消所有挂单...
2025-07-19 11:25:18,968 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-19 11:25:18,968 - INFO - [run] - 所有现有挂单已取消，准备启动策略...
2025-07-19 11:25:18,968 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/UnGF2yiQTi7gd26rwn8nqR54pDF3RWhj1dfg3J8s8otTux8QwmrJWnVBXSoaeGFT
2025-07-19 11:25:28,990 - ERROR - [run] - WebSocket连接失败 (1/10): timed out during opening handshake
2025-07-19 11:25:28,990 - INFO - [run] - 等待 10 秒后重连...
2025-07-19 11:25:54,745 - INFO - [main] - 启动前预检 API 连接...
2025-07-19 11:25:54,745 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-19 11:25:54,752 - INFO - [main] - 测试代理连接: http://httpbin.org/ip
2025-07-19 11:25:55,731 - INFO - [main] - 代理服务器连接正常 (测试URL: http://httpbin.org/ip)
2025-07-19 11:25:55,732 - INFO - [main] - 代理服务器测试通过
2025-07-19 11:25:55,737 - INFO - [main] - 正在测试 API 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/5
2025-07-19 11:25:55,819 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.08s
2025-07-19 11:25:55,820 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-19 11:25:55,820 - INFO - [_initialize_exchange] - 已通过 aiohttp_proxy 设置代理: http://127.0.0.1:7897
2025-07-19 11:25:55,827 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-19 11:25:55,827 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-19 11:25:55,829 - INFO - [setup] - 正在执行启动设置...
2025-07-19 11:25:55,829 - INFO - [test_connection] - 正在测试API连接... (尝试 1/5)
2025-07-19 11:25:55,829 - INFO - [test_connection] - 正在测试基础网络连接...
2025-07-19 11:25:55,834 - INFO - [test_connection] - 测试端点: https://fapi.binance.com/fapi/v1/ping
2025-07-19 11:25:55,914 - INFO - [test_connection] - 端点测试成功: https://fapi.binance.com/fapi/v1/ping - {}
2025-07-19 11:25:55,914 - INFO - [test_connection] - 正在测试CCXT交易所连接...
2025-07-19 11:25:56,397 - INFO - [test_connection] - CCXT Ping 成功: {}
2025-07-19 11:25:56,397 - INFO - [test_connection] - 正在测试服务器时间获取...
2025-07-19 11:25:56,732 - INFO - [test_connection] - 服务器时间获取成功: 1752895555798
2025-07-19 11:25:56,732 - INFO - [test_connection] - 正在加载市场数据...
2025-07-19 11:26:00,764 - INFO - [test_connection] - 市场数据加载成功
2025-07-19 11:26:00,764 - INFO - [test_connection] - 交易对 SOL/USDT:USDT 验证成功
2025-07-19 11:26:00,764 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-19 11:26:02,024 - INFO - [test_connection] - Ticker 获取成功: SOL/USDT:USDT @ 177.38
2025-07-19 11:26:02,024 - INFO - [test_connection] - 正在测试账户余额获取...
2025-07-19 11:26:03,287 - INFO - [test_connection] - 余额获取成功，USDT余额: 1656.93173557
2025-07-19 11:26:03,288 - INFO - [test_connection] - API连接测试完成！
2025-07-19 11:26:03,288 - INFO - [setup] - 正在加载市场数据...
2025-07-19 11:26:07,503 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-19 11:26:08,764 - INFO - [set_hedge_mode] - 当前已是双向持仓模式，无需更改
2025-07-19 11:26:10,031 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-19 11:26:14,887 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.36
2025-07-19 11:26:14,890 - INFO - [_internal_state_sync] - 同步到 0 个有效挂单
2025-07-19 11:26:18,617 - INFO - [fetch_listen_key] - 成功获取新的 listen key: UnGF2yiQTi...
2025-07-19 11:26:18,617 - INFO - [setup] - 初始化设置完成。
2025-07-19 11:26:18,617 - INFO - [setup] - 当前持仓: 多头=0.0, 空头=0.0
2025-07-19 11:26:22,277 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.38
2025-07-19 11:26:22,277 - INFO - [_internal_state_sync] - 同步到 0 个有效挂单
2025-07-19 11:26:22,277 - INFO - [cancel_all_open_orders] - 正在尝试取消所有挂单...
2025-07-19 11:26:23,548 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-19 11:26:23,549 - INFO - [run] - 所有现有挂单已取消，准备启动策略...
2025-07-19 11:26:23,549 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/UnGF2yiQTi7gd26rwn8nqR54pDF3RWhj1dfg3J8s8otTux8QwmrJWnVBXSoaeGFT
2025-07-19 11:26:25,385 - INFO - [run] - WebSocket 连接成功。
2025-07-19 11:26:25,386 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdt@bookTicker'], 'id': 1}
2025-07-19 11:26:29,048 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.34
2025-07-19 11:26:29,048 - INFO - [_internal_state_sync] - 同步到 0 个有效挂单
2025-07-19 11:26:29,050 - INFO - [execute_argm_strategy] - 网格重绘触发！新中枢: 172.8206, 新范围: [166.9430, 178.6982]
2025-07-19 11:26:29,050 - INFO - [rebalance_grid_orders] - === rebalance_grid_orders函数开始执行 ===
2025-07-19 11:26:29,051 - INFO - [rebalance_grid_orders] - 开始状态同步 (锁外)...
2025-07-19 11:26:32,708 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.34
2025-07-19 11:26:32,708 - INFO - [_internal_state_sync] - 同步到 0 个有效挂单
2025-07-19 11:26:32,708 - INFO - [rebalance_grid_orders] - 状态同步完成 (锁外)
2025-07-19 11:26:32,708 - INFO - [rebalance_grid_orders] - 网格需要调整: 现有0个订单 -> 目标30个订单
2025-07-19 11:26:32,708 - INFO - [rebalance_grid_orders] - 调试信息 - 交易方向: LONG_ONLY, 当前价格: 177.34, 最佳买价: 177.34, 最佳卖价: 177.35
2025-07-19 11:26:32,708 - INFO - [rebalance_grid_orders] - 调试信息 - 现有订单价格: set()
2025-07-19 11:26:32,709 - INFO - [rebalance_grid_orders] - 调试信息 - 目标网格价格: [167.02, 167.05, 167.1, 167.16, 167.25, 167.37, 167.54, 167.77, 168.07, 168.48, 169.0, 169.65, 170.43, 171.33, 172.32, 173.33, 174.31, 175.21, 175.99, 176.64, 177.16, 177.57, 177.87, 178.1, 178.27, 178.39, 178.48, 178.54, 178.59, 178.62]
2025-07-19 11:26:32,709 - INFO - [rebalance_grid_orders] - 调试信息 - 识别到需要下单的订单数量: 21
2025-07-19 11:26:32,709 - INFO - [rebalance_grid_orders] - 调试信息 - 待下单详情: [{'side': 'buy', 'price': 167.02, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 167.05, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 167.1, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 167.16, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 167.25, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 167.37, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 167.54, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 167.77, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 168.07, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 168.48, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 169.0, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 169.65, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 170.43, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 171.33, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 172.32, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 173.33, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 174.31, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 175.21, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 175.99, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 176.64, 'positionSide': 'LONG'}, {'side': 'buy', 'price': 177.16, 'positionSide': 'LONG'}]
2025-07-19 11:26:32,710 - INFO - [rebalance_grid_orders] - 开始执行订单操作: 取消0个, 创建21个
2025-07-19 11:26:32,710 - INFO - [rebalance_grid_orders] - 准备创建 21 个新订单
2025-07-19 11:26:32,710 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 167.0200
2025-07-19 11:26:33,973 - INFO - [place_order] - 下单成功: x-argm-1752895592710-8992
2025-07-19 11:26:34,078 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 167.0500
2025-07-19 11:26:38,944 - INFO - [place_order] - 下单成功: x-argm-1752895594078-f69f
2025-07-19 11:26:39,051 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 167.1000
2025-07-19 11:26:43,908 - INFO - [place_order] - 下单成功: x-argm-1752895599051-9323
2025-07-19 11:26:44,000 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 167.1600
2025-07-19 11:26:48,868 - INFO - [place_order] - 下单成功: x-argm-1752895604000-6665
2025-07-19 11:26:48,957 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 167.2500
2025-07-19 11:26:53,826 - INFO - [place_order] - 下单成功: x-argm-1752895608957-e751
2025-07-19 11:26:53,937 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 167.3700
2025-07-19 11:26:58,803 - INFO - [place_order] - 下单成功: x-argm-1752895613937-03a8
2025-07-19 11:26:58,908 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 167.5400
2025-07-19 11:27:03,761 - INFO - [place_order] - 下单成功: x-argm-1752895618908-c0fb
2025-07-19 11:27:03,876 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 167.7700
2025-07-19 11:27:08,749 - INFO - [place_order] - 下单成功: x-argm-1752895623876-bfca
2025-07-19 11:27:08,855 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 168.0700
2025-07-19 11:27:13,720 - INFO - [place_order] - 下单成功: x-argm-1752895628855-859a
2025-07-19 11:27:13,833 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 168.4800
2025-07-19 11:27:18,692 - INFO - [place_order] - 下单成功: x-argm-1752895633833-7034
2025-07-19 11:27:18,804 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 169.0000
2025-07-19 11:27:23,678 - INFO - [place_order] - 下单成功: x-argm-1752895638804-bd66
2025-07-19 11:27:23,788 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 169.6500
2025-07-19 11:27:28,650 - INFO - [place_order] - 下单成功: x-argm-1752895643788-99aa
2025-07-19 11:27:28,757 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 170.4300
2025-07-19 11:27:33,617 - INFO - [place_order] - 下单成功: x-argm-1752895648757-cd87
2025-07-19 11:27:33,729 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 171.3300
2025-07-19 11:27:38,591 - INFO - [place_order] - 下单成功: x-argm-1752895653729-0529
2025-07-19 11:27:38,696 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 172.3200
2025-07-19 11:27:43,562 - INFO - [place_order] - 下单成功: x-argm-1752895658696-9e8f
2025-07-19 11:27:43,667 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 173.3300
2025-07-19 11:27:48,528 - INFO - [place_order] - 下单成功: x-argm-1752895663667-4e25
2025-07-19 11:27:48,639 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 174.3100
2025-07-19 11:27:53,512 - INFO - [place_order] - 下单成功: x-argm-1752895668639-a5d4
2025-07-19 11:27:53,626 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 175.2100
2025-07-19 11:27:58,489 - INFO - [place_order] - 下单成功: x-argm-1752895673626-8640
2025-07-19 11:27:58,603 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 175.9900
2025-07-19 11:28:03,475 - INFO - [place_order] - 下单成功: x-argm-1752895678603-1d71
2025-07-19 11:28:03,582 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 176.6400
2025-07-19 11:28:08,451 - INFO - [place_order] - 下单成功: x-argm-1752895683582-3ae3
2025-07-19 11:28:08,564 - INFO - [place_order] - 尝试下单 (1/3): buy LONG 0.1100 @ 177.1600
2025-07-19 11:28:13,416 - INFO - [place_order] - 下单成功: x-argm-1752895688564-4ec7
2025-07-19 11:28:13,517 - INFO - [rebalance_grid_orders] - 成功创建 21/21 个订单
2025-07-19 11:28:20,783 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.36
2025-07-19 11:28:20,783 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:28:20,783 - INFO - [rebalance_grid_orders] - === rebalance_grid_orders函数正常完成 ===
2025-07-19 11:28:20,783 - WARNING - [run] - WebSocket连接已关闭 (1006): 
2025-07-19 11:28:20,784 - INFO - [run] - 等待 5 秒后重连...
2025-07-19 11:28:27,066 - INFO - [fetch_listen_key] - 成功获取新的 listen key: UnGF2yiQTi...
2025-07-19 11:28:27,067 - INFO - [run] - 已获取新的ListenKey
2025-07-19 11:28:27,067 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/UnGF2yiQTi7gd26rwn8nqR54pDF3RWhj1dfg3J8s8otTux8QwmrJWnVBXSoaeGFT
2025-07-19 11:28:27,263 - INFO - [run] - WebSocket 连接成功。
2025-07-19 11:28:27,264 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdt@bookTicker'], 'id': 1}
2025-07-19 11:28:30,928 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.37
2025-07-19 11:28:30,928 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:29:34,965 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.36
2025-07-19 11:29:34,965 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:30:40,326 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.37
2025-07-19 11:30:40,327 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:31:45,654 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.46
2025-07-19 11:31:45,654 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:32:51,113 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.5
2025-07-19 11:32:51,113 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:33:56,511 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.53
2025-07-19 11:33:56,512 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:35:01,791 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.53
2025-07-19 11:35:01,792 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:36:06,834 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.47
2025-07-19 11:36:06,834 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:36:47,316 - WARNING - [run] - WebSocket连接已关闭 (1006): 
2025-07-19 11:36:47,317 - INFO - [run] - 等待 5 秒后重连...
2025-07-19 11:36:54,815 - INFO - [fetch_listen_key] - 成功获取新的 listen key: UnGF2yiQTi...
2025-07-19 11:36:54,817 - INFO - [run] - 已获取新的ListenKey
2025-07-19 11:36:54,817 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/UnGF2yiQTi7gd26rwn8nqR54pDF3RWhj1dfg3J8s8otTux8QwmrJWnVBXSoaeGFT
2025-07-19 11:37:02,137 - INFO - [run] - WebSocket 连接成功。
2025-07-19 11:37:02,138 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdt@bookTicker'], 'id': 1}
2025-07-19 11:37:05,799 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.5
2025-07-19 11:37:05,799 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:37:52,171 - WARNING - [run] - WebSocket连接已关闭 (1006): 
2025-07-19 11:37:52,172 - INFO - [run] - 等待 5 秒后重连...
2025-07-19 11:37:58,493 - INFO - [fetch_listen_key] - 成功获取新的 listen key: UnGF2yiQTi...
2025-07-19 11:37:58,493 - INFO - [run] - 已获取新的ListenKey
2025-07-19 11:37:58,493 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/UnGF2yiQTi7gd26rwn8nqR54pDF3RWhj1dfg3J8s8otTux8QwmrJWnVBXSoaeGFT
2025-07-19 11:37:58,889 - INFO - [run] - WebSocket 连接成功。
2025-07-19 11:37:58,889 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdt@bookTicker'], 'id': 1}
2025-07-19 11:38:02,546 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.62
2025-07-19 11:38:02,546 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:39:06,278 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.56
2025-07-19 11:39:06,278 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:40:11,495 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.65
2025-07-19 11:40:11,495 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:41:16,567 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.63
2025-07-19 11:41:16,568 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:42:21,534 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.59
2025-07-19 11:42:21,535 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:43:26,851 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.66
2025-07-19 11:43:26,851 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:44:31,762 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.55
2025-07-19 11:44:31,763 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
2025-07-19 11:45:36,998 - WARNING - [_internal_state_sync] - Ticker数据缺少bid/ask，仅使用last价格更新: 177.57
2025-07-19 11:45:36,999 - INFO - [_internal_state_sync] - 同步到 21 个有效挂单
